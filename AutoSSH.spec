# -*- mode: python ; coding: utf-8 -*-

"""
AutoSSH PyInstaller 配置文件
用于将Python项目打包为独立的可执行文件
"""

import os

# 获取项目根目录
project_root = os.path.dirname(os.path.abspath(SPEC))

# 检查文件是否存在的辅助函数
def check_file_exists(file_path, description=""):
    """检查文件是否存在"""
    if os.path.exists(file_path):
        print(f"[存在] {description}: {file_path}")
        return True
    else:
        print(f"[缺失] {description}: {file_path}")
        return False

# 数据文件列表 - 包含配置、数据和资源文件
datas = []

# 配置文件
config_files = [
    ('config/config.py', 'config', '主配置文件'),
    ('config/config_template.py', 'config', '配置模板文件')
]

for file_path, dest_dir, desc in config_files:
    full_path = os.path.join(project_root, file_path)
    if check_file_exists(full_path, desc):
        datas.append((full_path, dest_dir))

# 数据文件 - 检查多个可能的IP文件
ip_files = ['data/ip.xlsx', 'data/ip2.xlsx']
ip_file_added = False

for ip_file in ip_files:
    full_path = os.path.join(project_root, ip_file)
    if check_file_exists(full_path, f'IP数据文件 ({ip_file})'):
        datas.append((full_path, 'data'))
        ip_file_added = True
        break

if not ip_file_added:
    print("[警告] 未找到IP数据文件，程序可能无法正常运行")

# 图标文件
ico_path = os.path.join(project_root, 'ico.ico')
if check_file_exists(ico_path, '应用图标'):
    datas.append((ico_path, '.'))

# 文档文件
doc_files = [
    ('docs/README.md', 'docs', '说明文档'),
    ('docs/优化说明.md', 'docs', '优化说明文档')
]

for file_path, dest_dir, desc in doc_files:
    full_path = os.path.join(project_root, file_path)
    if check_file_exists(full_path, desc):
        datas.append((full_path, dest_dir))

# 依赖文件
req_path = os.path.join(project_root, 'requirements.txt')
if check_file_exists(req_path, '依赖文件'):
    datas.append((req_path, '.'))

print(f"\n总共包含 {len(datas)} 个数据文件")

# 隐藏导入 - 确保所有必要的模块都被包含
hiddenimports = [
    # 核心模块
    'autossh',
    'autossh.main',
    'autossh.core',
    'autossh.core.logger',
    'autossh.core.config_loader', 
    'autossh.core.ssh_agent',
    'autossh.web',
    'autossh.web.web_server',
    'autossh.automation',
    'autossh.automation.browser',
    'autossh.utils',
    'autossh.utils.file_check',
    'autossh.utils.utils',
    
    # 第三方库
    'pandas',
    'numpy',
    'paramiko',
    'flask',
    'ansi2html',
    'DrissionPage',
    'psutil',
    'requests',
    'openpyxl',
    
    # 标准库
    'logging.handlers',
    'concurrent.futures',
    'threading',
    'weakref',
    'atexit',
    'signal',
    'tempfile',
    'shutil',
    'urllib.parse',
    'pathlib',
    'importlib.util',
    'importlib',
]

# 分析配置
a = Analysis(
    [os.path.join(project_root, 'src', 'launcher.py')],  # 主启动文件
    pathex=[
        project_root,
        os.path.join(project_root, 'src'),
    ],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 排除不需要的模块以减小文件大小
        'tkinter',
        'matplotlib',
        'scipy',
        'PIL',
        'cv2',
        'torch',
        'tensorflow',
    ],
    noarchive=False,
    optimize=0,
)

# Python字节码文件
pyz = PYZ(a.pure, a.zipped_data)

# 可执行文件配置
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='AutoSSH',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=os.path.join(project_root, 'ico.ico'),  # 应用程序图标
)
