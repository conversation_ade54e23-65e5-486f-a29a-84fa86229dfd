#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SSH代理模块
处理SSH连接和命令执行
"""

import paramiko
import socket
import time
import re
import os
import threading
import atexit
import weakref
from ansi2html import Ansi2HTMLConverter
from .logger import get_logger

# 日志实例将在需要时获取
log = None

def _get_log():
    """获取日志实例（延迟初始化）"""
    global log
    if log is None:
        log = get_logger()
    return log

# 实例化转换器
conv = Ansi2HTMLConverter(inline=True, dark_bg=True, scheme="solarized")

# 用于跟踪所有活动的SSH连接
_active_connections = weakref.WeakSet()
_connections_lock = threading.Lock()

def cleanup_all_connections():
    """清理所有活动的SSH连接"""
    with _connections_lock:
        for ssh in list(_active_connections):
            try:
                if ssh and ssh.get_transport() and ssh.get_transport().is_active():
                    ssh.close()
                    _get_log().debug(f"已关闭SSH连接")
            except Exception as e:
                pass
        _active_connections.clear()

# 注册程序退出时的清理函数
atexit.register(cleanup_all_connections)

class FileCheckResult:
    """文件检查结果"""
    def __init__(self, store, ip):
        self.store = store
        self.ip = ip
        self.file_exists = False
        self.local_size = None
        self.cloud_size = None
        self.is_consistent = False
        self.error = None

class SSHAgent:
    """SSH连接代理类"""
    
    def __init__(self, ip, password, store, port=22, username='root', timeout=15, cmd_timeout=30):
        """初始化SSH代理
        
        Args:
            ip (str): 目标服务器IP
            password (str): 密码
            store (str): 门店名称
            port (int, optional): SSH端口. 默认为 22.
            username (str, optional): 用户名. 默认为 'root'.
            timeout (int, optional): 连接超时. 默认为 15.
            cmd_timeout (int, optional): 命令超时. 默认为 30.
        """
        self.ip = ip.strip()
        self.password = str(password).strip()
        self.store = store
        self.port = port
        self.username = username
        self.timeout = timeout
        self.cmd_timeout = cmd_timeout
        
        self.ssh = paramiko.SSHClient()
        self.ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        self.output = []
        self.duration = 0
        self.success = False
        self.file_check_result = FileCheckResult(store, ip)
        
        # 添加连接监控的守护线程
        self._monitor_thread = None
        self._is_connected = False

    def _record(self, message, color=None):
        """记录带颜色的输出
        
        Args:
            message (str): 要记录的消息
            color (str, optional): ANSI颜色代码. 默认为 None.
        """
        if message.startswith("root@"):
            # 替换命令提示符格式
            message = message.replace("root@", "[root@")
            message = message.replace(" ~$ ", " ~]# ")
        if color:
            self.output.append(f"\033[{color}m{message}\033[0m")
        else:
            self.output.append(message)

    def _check_file_in_output(self, output, file_to_check, file_path, cloud_file_url, cloud_size):
        """检查输出中是否包含目标文件信息
        
        Args:
            output (str): 命令输出
            file_to_check (str): 要检查的文件名
            file_path (str): 文件路径
            cloud_file_url (str): 云端文件URL
            cloud_size (float): 云端文件大小(MB)
        """
        # 设置云端文件大小
        self.file_check_result.cloud_size = cloud_size
        
        # 正则表达式匹配文件行
        file_path = os.path.join(file_path, file_to_check)
        pattern = fr".*{file_to_check}"
        
        lines = output.split('\n')
        for line in lines:
            if file_to_check in line:
                self.file_check_result.file_exists = True
                
                # 尝试提取文件大小
                size_match = re.search(r'(\d+)([KMG])', line)
                if size_match:
                    size_value = float(size_match.group(1))
                    size_unit = size_match.group(2)
                    
                    # 转换为MB
                    if size_unit == 'K':
                        size_mb = size_value / 1024
                    elif size_unit == 'M':
                        size_mb = size_value
                    elif size_unit == 'G':
                        size_mb = size_value * 1024
                    else:
                        size_mb = None
                        
                    self.file_check_result.local_size = size_mb
                    
                    # 检查一致性
                    if cloud_size is not None and size_mb is not None:
                        # 允许5%的误差
                        error_margin = 0.05
                        diff_ratio = abs(cloud_size - size_mb) / max(cloud_size, size_mb)
                        self.file_check_result.is_consistent = diff_ratio <= error_margin
                
                break

    def _start_connection_monitor(self):
        """启动连接监控线程，确保连接在指定时间内关闭"""
        def monitor_connection():
            # 等待连接超时时间的两倍，足够执行完命令
            max_wait_time = (self.timeout + self.cmd_timeout) * 2
            start_time = time.time()
            
            while time.time() - start_time < max_wait_time and self._is_connected:
                time.sleep(1)
            
            # 如果连接仍然活跃，强制关闭
            if self._is_connected:
                try:
                    _get_log().warning(f"连接监控: 强制关闭到 {self.ip} 的连接")
                    self.close_connection(force=True)
                except:
                    pass
        
        self._monitor_thread = threading.Thread(target=monitor_connection)
        self._monitor_thread.daemon = True
        self._monitor_thread.start()

    def close_connection(self, force=False):
        """关闭SSH连接
        
        Args:
            force (bool, optional): 是否强制关闭. 默认为 False.
        """
        try:
            if self.ssh:
                if force:
                    # 获取传输层并直接关闭
                    transport = self.ssh.get_transport()
                    if transport and transport.is_active():
                        transport.close()
                self.ssh.close()
                
                # 从活动连接集合中移除
                with _connections_lock:
                    if self.ssh in _active_connections:
                        _active_connections.remove(self.ssh)
                
                self._is_connected = False
                _get_log().debug(f"已关闭到 {self.ip} 的连接")
        except Exception as e:
            _get_log().error(f"关闭连接时出错: {str(e)}")

    def _map_command(self, command):
        """映射命令，特别是将ll转换为ls -l
        
        Args:
            command (str): 原始命令，可能包含多个由分号分隔的命令
            
        Returns:
            tuple: (映射后的命令, 是否已映射)
        """
        # 处理可能包含多个命令的情况
        if ';' in command:
            commands = command.split(';')
            mapped_commands = []
            mapped_any = False
            
            for cmd in commands:
                mapped_cmd, is_mapped = self._map_single_command(cmd.strip())
                mapped_commands.append(mapped_cmd)
                if is_mapped:
                    mapped_any = True
            
            return ';'.join(mapped_commands), mapped_any
        else:
            return self._map_single_command(command.strip())
    
    def _map_single_command(self, command):
        """映射单个命令
        
        Args:
            command (str): 单个原始命令
            
        Returns:
            tuple: (映射后的命令, 是否已映射)
        """
        # 处理ll命令
        command = command.strip()
        if command.startswith('ll'):
            # 解析ll命令参数
            parts = command[2:].strip().split()
            options = []
            path = []
            
            # 分离选项和路径
            for part in parts:
                if part.startswith('-'):
                    options.append(part)
                else:
                    path.append(part)
            
            # 正确组合ls -l命令
            # 如果有-h选项，合并成-lh，否则使用-l
            has_h = False
            other_options = []
            for opt in options:
                if 'h' in opt:
                    has_h = True
                else:
                    other_options.append(opt)
            
            # 构建选项部分
            ls_options = '-l' + ('h' if has_h else '')
            
            # 构建完整命令
            full_cmd = f"ls {ls_options} {' '.join(other_options)} {' '.join(path)}".strip()
            return full_cmd, True
        
        return command, False

    def execute(self, command, file_check_enabled=False, file_to_check=None, file_path=None,
                cloud_file_url=None, cloud_size=None, file_check_results=None):
        """执行SSH命令

        Args:
            command (str): 要执行的命令
            file_check_enabled (bool, optional): 是否启用文件检查. 默认为 False.
            file_to_check (str, optional): 要检查的文件名. 默认为 None.
            file_path (str, optional): 文件路径. 默认为 None.
            cloud_file_url (str, optional): 云端文件URL. 默认为 None.
            cloud_size (float, optional): 云端文件大小. 默认为 None.
            file_check_results (list, optional): 文件检查结果列表. 默认为 None.

        Returns:
            bool: 命令是否成功执行
        """
        start_time = time.time()
        try:
            # 连接配置 - 这条日志会被SSHLogFilter过滤
            _get_log().info(f"正在连接 {self.ip} ({self.store})...")
            self.ssh.connect(
                hostname=self.ip,
                port=self.port,
                username=self.username,
                password=self.password,
                timeout=self.timeout,
                banner_timeout=20,
                look_for_keys=False
            )

            # 标记连接已建立
            self._is_connected = True

            # 添加到活动连接集合
            with _connections_lock:
                _active_connections.add(self.ssh)

            # 启动连接监控
            self._start_connection_monitor()

            # 获取主机信息
            stdin, stdout, stderr = self.ssh.exec_command('hostname')
            hostname = stdout.read().decode().strip() or self.ip

            # 应用命令映射 - 记录原始命令但执行映射命令
            mapped_command, is_mapped = self._map_command(command)

            # 显示原始命令
            self._record(f"root@{hostname} ~$ {command}", "1;32")

            # 执行映射后的命令
            stdin, stdout, stderr = self.ssh.exec_command(
                f"export LANG=en_US.UTF-8; export TERM=xterm-256color; {mapped_command}",
                get_pty=True,
                timeout=self.cmd_timeout
            )

            # 获取输出
            out = stdout.read().decode().strip()
            err = stderr.read().decode().strip()
            if out:
                self.output.append(out)
                if file_check_enabled and file_to_check:
                    self._check_file_in_output(out, file_to_check, file_path, cloud_file_url, cloud_size)
            if err:
                self._record(err, "1;31")

            # 在命令执行完成后添加提示符
            self._record(f"[root@{hostname} ~]# ", "1;32")

            self.success = True
            _get_log().progress(self.store, self.ip, "√", "连接成功")

            # 添加文件检查结果到结果列表
            if file_check_enabled and file_check_results is not None:
                file_check_results.append(self.file_check_result)

            return True

        except paramiko.AuthenticationException:
            _get_log().progress(self.store, self.ip, "×", "密码错误")
            self.file_check_result.error = "密码错误"
            if file_check_enabled and file_check_results is not None:
                file_check_results.append(self.file_check_result)
            return False
        except socket.timeout:
            _get_log().progress(self.store, self.ip, "×", "连接超时")
            self.file_check_result.error = "连接超时"
            if file_check_enabled and file_check_results is not None:
                file_check_results.append(self.file_check_result)
            return False
        except Exception as e:
            _get_log().progress(self.store, self.ip, "×", f"错误: {str(e)}")
            self.file_check_result.error = str(e)
            if file_check_enabled and file_check_results is not None:
                file_check_results.append(self.file_check_result)
            return False
        finally:
            self.duration = time.time() - start_time
            try:
                self.close_connection()
            except:
                pass

    def get_html_output(self):
        """获取HTML格式的输出

        Returns:
            str: HTML格式的输出
        """
        return conv.convert('\n'.join(self.output))

    def get_web_result(self):
        """获取Web显示结果

        Returns:
            dict: Web显示结果字典
        """
        return {
            "ip": self.ip,
            "store": self.store,
            "output": self.get_html_output(),
            "time": f"{self.duration:.2f}s",
            "style": "",
            "icon": ""
        }
