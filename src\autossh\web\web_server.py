#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Web服务器模块
提供Web界面展示SSH执行结果
"""

import logging
import threading
import atexit
import time
from flask import Flask, render_template_string
from werkzeug.serving import make_server
from ..core.logger import get_logger

# 获取日志实例
log = get_logger()

class WebServer:
    """Web服务器类"""
    
    def __init__(self, web_host='0.0.0.0', web_port=5000, font_size=15, line_height=1.5):
        """初始化Web服务器
        
        Args:
            web_host (str, optional): 监听地址. 默认为 '0.0.0.0'.
            web_port (int, optional): 监听端口. 默认为 5000.
            font_size (int, optional): 终端字体大小. 默认为 15.
            line_height (float, optional): 终端行高. 默认为 1.5.
        """
        self.web_host = web_host
        self.web_port = web_port
        self.font_size = font_size
        self.line_height = line_height
        self.web_results = []
        self.server_thread = None
        self.server = None
        self.app = Flask(__name__)
        
        # 禁用Flask日志
        log_werkzeug = logging.getLogger('werkzeug')
        log_werkzeug.disabled = True
        self.app.logger.disabled = True
        
        # 设置路由
        self._setup_routes()
    
    def _setup_routes(self):
        """设置Flask路由"""
        
        @self.app.route('/')
        def index():
            """首页 - 显示所有服务器列表"""
            return render_template_string('''
            <!DOCTYPE html>
            <html>
            <head>
                <title>服务器列表</title>
                <style>
                    :root {
                        --term-bg: #000000;
                        --term-fg: #33ff00;
                        --term-font: "Ubuntu Mono", "Courier New", monospace;
                        --term-font-size: {{ font_size }}px;
                        --term-line-height: {{ line_height }};
                    }
                    
                    body { 
                        background: var(--term-bg);
                        margin: 0;
                        padding: 20px;
                        font-family: var(--term-font);
                        color: var(--term-fg);
                        font-size: var(--term-font-size);
                        line-height: var(--term-line-height);
                    }
                    
                    .server-list {
                        max-width: 800px;
                        margin: 0 auto;
                    }
                    
                    .server-item {
                        border: 1px solid #333;
                        margin: 10px 0;
                        padding: 15px;
                        background: rgba(0, 0, 0, 0.8);
                        border-radius: 5px;
                        cursor: pointer;
                        transition: all 0.3s;
                    }
                    
                    .server-item:hover {
                        transform: translateX(10px);
                        box-shadow: 0 0 10px rgba(51, 255, 0, 0.2);
                    }
                    
                    .store { color: #00ffff; }
                    .ip { color: #ffff00; margin-left: 10px; }
                    
                    a {
                        text-decoration: none;
                        color: inherit;
                    }
                </style>
            </head>
            <body>
                <div class="server-list">
                    <h1>服务器列表</h1>
                    {% for item in results %}
                    <a href="/server/{{ item.ip }}">
                        <div class="server-item">
                            <span class="store">{{ item.store }}</span>
                            <span class="ip">[{{ item.ip }}]</span>
                        </div>
                    </a>
                    {% endfor %}
                </div>
            </body>
            </html>
            ''', results=self.web_results, font_size=self.font_size, line_height=self.line_height)

        @self.app.route('/server/<ip>')
        def server_detail(ip):
            """服务器详情页"""
            result = next((item for item in self.web_results if item['ip'] == ip), None)
            if not result:
                return "Server not found", 404
                
            return render_template_string('''
            <!DOCTYPE html>
            <html>
            <head>
                <title>{{ result.store }} [{{ result.ip }}]</title>
                <style>
                    :root {
                        --term-bg: #000000;
                        --term-fg: #33ff00;
                        --term-font: "Ubuntu Mono", "Courier New", monospace;
                        --term-font-size: {{ font_size }}px;
                        --term-line-height: {{ line_height }};
                    }
                    
                    body { 
                        background: var(--term-bg);
                        margin: 0;
                        padding: 0;
                        font-family: var(--term-font);
                        color: var(--term-fg);
                        line-height: var(--term-line-height);
                        height: 100vh;
                        display: flex;
                        flex-direction: column;
                    }
                    
                    .terminal {
                        flex: 1;
                        border: 1px solid #333;
                        margin: 0;
                        padding: 20px;
                        background: rgba(0, 0, 0, 0.8);
                        position: relative;
                    }
                    
                    .content {
                        padding: 10px 0;
                        font-size: var(--term-font-size);
                        height: calc(100vh - 60px);
                        overflow-y: auto;
                    }
                    
                    .back-link {
                        display: block;
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100px;
                        height: 25px;
                        opacity: 0;
                        z-index: 10;
                        cursor: pointer;
                    }
                    
                    /* ANSI颜色覆盖 */
                    .ansi2html-content {
                        white-space: pre-wrap;
                        word-wrap: break-word;
                        font-size: var(--term-font-size);
                    }
                    
                    /* 滚动条样式 */
                    ::-webkit-scrollbar {
                        width: 10px;
                        height: 10px;
                    }
                    
                    ::-webkit-scrollbar-track {
                        background: #111;
                    }
                    
                    ::-webkit-scrollbar-thumb {
                        background: #333;
                        border-radius: 5px;
                    }
                    
                    ::-webkit-scrollbar-thumb:hover {
                        background: #444;
                    }
                </style>
            </head>
            <body>
                <div class="terminal">
                    <a href="/" class="back-link"></a>
                    <div class="content">{{ result.output|safe }}</div>
                </div>
            </body>
            </html>
            ''', result=result, font_size=self.font_size, line_height=self.line_height)
    
    def start(self):
        """启动Web服务器"""
        if self.server_thread and self.server_thread.is_alive():
            log.warning("Web服务器已在运行中")
            return
            
        self.server_thread = ServerThread(self)
        self.server_thread.daemon = True
        self.server_thread.start()
        
        # 添加退出处理
        atexit.register(self.stop)
        
        # 等待服务器启动
        time.sleep(0.5)
        
        # 使用更整洁的消息格式
        server_url = f"http://{self.web_host}:{self.web_port}/"
        log.info(f"✓ Web服务器已启动: {server_url}")
    
    def stop(self):
        """停止Web服务器"""
        if self.server_thread and hasattr(self.server_thread, 'server'):
            log.info("正在关闭Web服务器...")
            self.server_thread.shutdown()
            log.info("✓ Web服务器已关闭")
    
    def add_result(self, result):
        """添加Web展示结果
        
        Args:
            result (dict): Web展示结果字典
        """
        self.web_results.append(result)

class ServerThread(threading.Thread):
    """Web服务器线程类"""
    
    def __init__(self, web_server):
        """初始化Web服务器线程
        
        Args:
            web_server (WebServer): Web服务器实例
        """
        super().__init__()
        self.web_server = web_server
        self.server = None
        
    def run(self):
        """运行Web服务器线程"""
        try:
            self.server = make_server(
                host=self.web_server.web_host,
                port=self.web_server.web_port,
                app=self.web_server.app,
                threaded=True,
                passthrough_errors=True,
                ssl_context=None
            )
            self.web_server.server = self.server
            self.server.serve_forever()
        except Exception as e:
            log.error(f"Web服务启动失败: {str(e)}")
            
    def shutdown(self):
        """关闭Web服务器"""
        if self.server:
            self.server.shutdown()
