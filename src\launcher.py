#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AutoSSH 服务器监控工具 - 启动器
功能: 确保所有模块能够被正确加载，处理路径和导入问题
"""

import warnings
# 忽略 TripleDES 的废弃警告
warnings.filterwarnings("ignore", category=DeprecationWarning, module="paramiko")
warnings.filterwarnings("ignore", category=DeprecationWarning, module="cryptography")
# 更精确地忽略特定警告
warnings.filterwarnings("ignore", message="TripleDES has been moved to")

import os
import sys
import time
import traceback
import logging
import argparse
import platform
import signal
import atexit
from pathlib import Path

# 应用信息
APP_NAME = "AutoSSH"
APP_VERSION = "1.2.0"

# 全局变量，用于记录需要清理的资源
_resources_to_cleanup = []

def cleanup_resources():
    """清理所有注册的资源"""
    global _resources_to_cleanup
    for cleanup_func in reversed(_resources_to_cleanup):
        try:
            cleanup_func()
        except Exception as e:
            print(f"资源清理错误: {e}")

def register_cleanup(cleanup_func):
    """注册清理函数
    
    Args:
        cleanup_func (callable): 清理函数
    """
    global _resources_to_cleanup
    if cleanup_func not in _resources_to_cleanup:
        _resources_to_cleanup.append(cleanup_func)

def setup_signal_handlers():
    """设置信号处理器"""
    def signal_handler(signum, frame):
        """处理退出信号"""
        print("\n接收到终止信号，正在清理资源...")
        cleanup_resources()
        # 使用原始信号处理
        original_handler = signal.getsignal(signum)
        if callable(original_handler) and original_handler is not signal_handler:
            original_handler(signum, frame)
        else:
            sys.exit(1)
    
    try:
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        if hasattr(signal, 'SIGBREAK'):  # Windows
            signal.signal(signal.SIGBREAK, signal_handler)
    except (ValueError, AttributeError):
        # 某些环境可能不支持信号处理
        pass

def setup_environment():
    """设置环境变量和Python路径"""
    try:
        # 获取当前脚本所在目录
        if getattr(sys, 'frozen', False):
            # 如果是PyInstaller打包后的环境
            application_path = os.path.dirname(sys.executable)
        else:
            # 如果是源代码运行环境，获取项目根目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            # 从 src/launcher.py 回到项目根目录
            application_path = os.path.dirname(current_dir)
        
        # 确保src目录在Python路径的最前面，优先级最高
        src_path = os.path.join(application_path, 'src')
        if src_path in sys.path:
            sys.path.remove(src_path)
        sys.path.insert(0, src_path)

        # 确保当前目录在Python路径中，但优先级低于src
        if application_path in sys.path:
            sys.path.remove(application_path)
        sys.path.insert(1, application_path)
        
        # 设置工作目录
        os.chdir(application_path)
        
        # 确保目录结构
        ensure_directories(application_path)
        
        return application_path
    except Exception as e:
        print(f"环境设置错误: {e}")
        traceback.print_exc()
        return None

def ensure_directories(base_path):
    """确保必要的目录结构存在"""
    # 需要创建的目录
    required_dirs = [
        "output/logs", 
        "output/screenshots", 
        "output/reports",
        "config",
        "data"
    ]
    
    for directory in required_dirs:
        dir_path = os.path.join(base_path, directory)
        if not os.path.exists(dir_path):
            try:
                os.makedirs(dir_path)
                print(f"创建目录: {dir_path}")
            except Exception as e:
                print(f"无法创建目录 {dir_path}: {e}")

def setup_console_encoding():
    """设置控制台编码，适用于打包环境"""
    try:
        # 安全地检查sys.stdout是否存在且有encoding属性
        if hasattr(sys, 'stdout') and sys.stdout is not None and hasattr(sys.stdout, 'encoding'):
            if sys.stdout.encoding != 'utf-8':
                try:
                    sys.stdout.reconfigure(encoding='utf-8')
                except:
                    pass
    except Exception:
        # 忽略任何编码设置错误
        pass

def setup_logging(app_path):
    """设置日志系统"""
    try:
        logs_dir = os.path.join(app_path, "output", "logs")
        if not os.path.exists(logs_dir):
            os.makedirs(logs_dir)
        
        log_file = os.path.join(
            logs_dir,
            f"autossh_launcher_{time.strftime('%Y-%m-%d')}.log"
        )
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s [%(levelname)s] %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        # 注册日志清理
        def close_logging():
            logging.shutdown()
        
        register_cleanup(close_logging)
        
        # 记录启动信息
        logging.info(f"=== {APP_NAME} v{APP_VERSION} 启动 ===")
        logging.info(f"操作系统: {platform.system()} {platform.release()} ({platform.machine()})")
        logging.info(f"Python版本: {platform.python_version()}")
        logging.info(f"工作目录: {app_path}")
        
        return True
    except Exception as e:
        print(f"日志设置错误: {e}")
        traceback.print_exc()
        return False

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description=f"{APP_NAME} - 服务器监控工具")
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--version', action='store_true', help='显示版本信息')
    parser.add_argument('--nolog', action='store_true', help='禁用日志记录')
    parser.add_argument('--test-import', action='store_true', help='测试模块导入')
    
    return parser.parse_args()

def show_version():
    """显示版本信息"""
    print(f"{APP_NAME} 版本 {APP_VERSION}")
    print(f"运行环境: Python {platform.python_version()}")
    print(f"操作系统: {platform.system()} {platform.release()} ({platform.machine()})")

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")

    test_modules = [
        "autossh",
        "autossh.main",
        "autossh.core.logger",
        "autossh.core.config_loader",
        "autossh.core.ssh_agent",
        "autossh.web.web_server",
        "autossh.automation.browser",
        "autossh.utils.file_check",
        "autossh.utils.utils"
    ]

    success_count = 0
    for module_name in test_modules:
        try:
            __import__(module_name)
            print(f"  ✓ {module_name}")
            success_count += 1
        except Exception as e:
            print(f"  × {module_name}: {e}")

    print(f"\n导入测试完成: {success_count}/{len(test_modules)} 成功")
    return 0 if success_count == len(test_modules) else 1

def main():
    """主函数"""
    try:
        # 设置信号处理和资源清理
        setup_signal_handlers()
        atexit.register(cleanup_resources)
        
        # 解析命令行参数
        args = parse_arguments()
        
        # 处理版本信息请求
        if args.version:
            show_version()
            return 0

        # 处理导入测试请求
        if args.test_import:
            return test_imports()
        
        # 设置控制台编码
        setup_console_encoding()
        
        # 设置环境
        app_path = setup_environment()
        if not app_path:
            print("环境设置失败，程序退出")
            return 1
        
        # 设置日志系统
        if not args.nolog and not setup_logging(app_path):
            print("日志系统设置失败，但程序将继续运行")
        
        # 设置调试模式
        if args.debug:
            logging.getLogger().setLevel(logging.DEBUG)
            logging.debug("调试模式已启用")
        
        print(f"启动 {APP_NAME} v{APP_VERSION} ...")
        print(f"应用程序路径: {app_path}")
        
        # 等待所有模块加载完毕，避免导入错误
        time.sleep(1.0)  # 增加等待时间确保模块加载
        
        # 导入并运行主程序
        try:
            from autossh.main import main as run_main
            logging.info("主程序模块加载成功")
            return run_main()
        except ImportError as e:
            error_msg = f"导入错误: {e}"
            logging.error(error_msg)
            logging.error("可能是缺少必要的模块或模块路径不正确")
            logging.error(f"Python路径: {sys.path}")
            print(error_msg)
            print("可能是缺少必要的模块或模块路径不正确")
            traceback.print_exc()
            input("\n按回车键退出...")
            return 1
    except KeyboardInterrupt:
        print("\n用户中断执行")
        return 130
    except Exception as e:
        error_msg = f"启动错误: {e}"
        try:
            logging.error(error_msg)
            logging.error(traceback.format_exc())
        except:
            pass
        print(error_msg)
        traceback.print_exc()
        input("\n按回车键退出...")
        return 1
    finally:
        # 确保所有清理工作完成
        cleanup_resources()

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
