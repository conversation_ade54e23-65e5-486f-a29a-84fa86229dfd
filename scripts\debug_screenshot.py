#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
截图调试脚本 - 使用DrissionPage进行截图测试
"""

import os
import sys
import time
from datetime import datetime

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(__file__)), 'src'))

def debug_screenshot():
    """调试截图功能"""
    print("=== 截图调试脚本 (DrissionPage版本) ===\n")

    try:
        from DrissionPage import ChromiumPage, ChromiumOptions
    except ImportError as e:
        print(f"导入DrissionPage失败: {e}")
        print("请安装: pip install DrissionPage")
        return
    
    # 测试URL
    test_url = "http://127.0.0.1:5000/server/************"

    # 截图保存目录
    project_root = os.path.dirname(os.path.dirname(__file__))
    debug_dir = os.path.join(project_root, "output", "debug_screenshots")
    os.makedirs(debug_dir, exist_ok=True)

    print(f"测试URL: {test_url}")
    print(f"保存目录: {debug_dir}")

    # DrissionPage配置
    co = ChromiumOptions()
    co.headless()  # 无头模式
    co.set_argument('--no-sandbox')
    co.set_argument('--disable-dev-shm-usage')
    co.set_argument('--disable-gpu')
    co.set_argument('--disable-extensions')
    co.set_argument('--disable-plugins')
    co.set_argument('--force-device-scale-factor=1')
    co.set_argument('--window-size=1920,1080')

    print("DrissionPage配置完成")

    page = None
    
    try:
        print("\n启动Chrome浏览器...")

        # 创建DrissionPage页面对象
        page = ChromiumPage(addr_or_opts=co)
        print("DrissionPage浏览器启动成功")

        # 访问页面
        print(f"\n访问页面: {test_url}")
        page.get(test_url)

        # 等待页面加载完成
        print("等待页面加载...")

        # 等待终端容器出现
        try:
            terminal_container = page.wait.ele_loaded('.terminal-container', timeout=15)
            if terminal_container:
                print("终端容器加载完成")
            else:
                print("未找到终端容器")
        except Exception as e:
            print(f"等待终端容器超时: {e}")

        # 额外等待内容渲染
        time.sleep(3)

        # 获取页面信息
        page_title = page.title
        page_size = page.rect.size
        print(f"页面标题: {page_title}")
        print(f"窗口大小: {page_size}")

        # 检查页面内容
        try:
            body_text = page.text[:200] + "..." if len(page.text) > 200 else page.text
            print(f"页面内容预览: {body_text}")
        except Exception as e:
            print(f"获取页面内容失败: {e}")
        
        # 生成截图文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 测试不同的截图方式
        screenshot_tests = [
            {
                "name": "标准截图 1920x1080",
                "filename": f"debug_standard_{timestamp}.png",
                "method": "standard",
                "width": 1920,
                "height": 1080
            },
            {
                "name": "高清截图 2560x1440",
                "filename": f"debug_2k_{timestamp}.png",
                "method": "high_resolution",
                "width": 2560,
                "height": 1440
            },
            {
                "name": "4K截图 3840x2160",
                "filename": f"debug_4k_{timestamp}.png",
                "method": "ultra_high",
                "width": 3840,
                "height": 2160
            },
            {
                "name": "全页面截图",
                "filename": f"debug_fullpage_{timestamp}.png",
                "method": "fullpage",
                "width": 1920,
                "height": None  # 动态高度
            },
            {
                "name": "全页面高清截图",
                "filename": f"debug_fullpage_hd_{timestamp}.png",
                "method": "fullpage_hd",
                "width": 2560,
                "height": None
            },
            {
                "name": "移动端视图截图",
                "filename": f"debug_mobile_{timestamp}.png",
                "method": "mobile",
                "width": 375,
                "height": 812
            },
            {
                "name": "平板视图截图",
                "filename": f"debug_tablet_{timestamp}.png",
                "method": "tablet",
                "width": 768,
                "height": 1024
            },
            {
                "name": "超宽屏截图",
                "filename": f"debug_ultrawide_{timestamp}.png",
                "method": "ultrawide",
                "width": 3440,
                "height": 1440
            }
        ]
        
        print(f"\n开始截图测试...")
        
        for test in screenshot_tests:
            try:
                print(f"\n{test['name']}...")
                filepath = os.path.join(debug_dir, test['filename'])

                # 设置窗口大小
                width = test['width']
                height = test['height']

                if test['method'] in ['fullpage', 'fullpage_hd']:
                    # 全页面截图 - 获取页面总高度
                    total_height = page.run_js("return Math.max(document.body.scrollHeight, document.documentElement.scrollHeight)")
                    page.set.window.size(width, total_height)
                    print(f"    设置全页面尺寸: {width}x{total_height}")
                else:
                    # 固定尺寸截图
                    page.set.window.size(width, height)
                    print(f"    设置窗口尺寸: {width}x{height}")

                # 等待页面重新渲染
                time.sleep(2)

                # 执行截图
                try:
                    page.get_screenshot(path=filepath, full_page=False)
                    success = True
                except Exception as screenshot_error:
                    print(f"    ⚠️ 截图异常: {screenshot_error}")
                    success = False

                if success and os.path.exists(filepath):
                    file_size = os.path.getsize(filepath)
                    file_size_mb = file_size / 1024 / 1024
                    print(f"    截图成功: {test['filename']} ({file_size_mb:.2f} MB)")

                    # 获取图片实际尺寸
                    try:
                        from PIL import Image
                        with Image.open(filepath) as img:
                            actual_width, actual_height = img.size
                            print(f"    实际图片尺寸: {actual_width}x{actual_height}")
                    except ImportError:
                        print("    安装PIL可查看图片尺寸: pip install Pillow")
                    except Exception as e:
                        print(f"    获取图片尺寸失败: {e}")
                else:
                    print(f"    截图失败: {test['filename']}")

            except Exception as e:
                print(f"    {test['name']}失败: {e}")
                import traceback
                traceback.print_exc()

            # 恢复标准窗口大小
            try:
                page.set.window.size(1920, 1080)
                time.sleep(0.5)
            except Exception:
                pass
        
        # 页面元素分析
        print(f"\n页面元素分析...")
        try:
            # 查找关键元素
            elements_to_check = [
                ("terminal-container", ".terminal-container"),
                ("terminal-output", ".terminal-output"),
                ("server-info", ".server-info"),
                ("pre", "tag:pre"),
                ("code", "tag:code"),
            ]

            for element_name, selector in elements_to_check:
                try:
                    elements = page.eles(selector)
                    if elements:
                        print(f"  找到 {element_name}: {len(elements)} 个")
                        if elements[0].states.is_displayed:
                            size = elements[0].rect.size
                            print(f"    第一个元素大小: {size}")
                        else:
                            print(f"    元素不可见")
                    else:
                        print(f"  未找到 {element_name}")
                except Exception as e:
                    print(f"  检查 {element_name} 失败: {e}")

        except Exception as e:
            print(f"页面元素分析失败: {e}")

        print(f"\n截图文件保存在: {debug_dir}")

        # 生成质量对比HTML报告
        generate_comparison_report(debug_dir, screenshot_tests, timestamp)

    except Exception as e:
        print(f"截图调试失败: {e}")
        import traceback
        traceback.print_exc()

    finally:
        if page:
            print("\n关闭浏览器...")
            page.quit()
            print("浏览器已关闭")

    print(f"\n=== 截图调试完成 ===")

def generate_comparison_report(debug_dir, screenshot_tests, timestamp):
    """生成截图质量对比HTML报告"""
    try:
        print("\n生成质量对比报告...")

        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>截图质量对比报告 - {timestamp}</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }}
        .header {{
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }}
        .screenshot-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        .screenshot-item {{
            background: white;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }}
        .screenshot-item:hover {{
            transform: translateY(-5px);
        }}
        .screenshot-title {{
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }}
        .screenshot-info {{
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
        }}
        .screenshot-img {{
            width: 100%;
            border: 1px solid #ddd;
            border-radius: 5px;
            cursor: pointer;
        }}
        .modal {{
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.9);
        }}
        .modal-content {{
            margin: auto;
            display: block;
            width: 90%;
            max-width: 1200px;
            max-height: 90%;
            margin-top: 2%;
        }}
        .close {{
            position: absolute;
            top: 15px;
            right: 35px;
            color: #f1f1f1;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
        }}
        .stats {{
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>📸 截图质量对比报告</h1>
        <p>生成时间: {timestamp}</p>
        <p>测试URL: http://127.0.0.1:5000/server/************</p>
    </div>

    <div class="screenshot-grid">
"""

        # 添加每个截图
        for test in screenshot_tests:
            filepath = os.path.join(debug_dir, test['filename'])
            if os.path.exists(filepath):
                file_size = os.path.getsize(filepath)
                file_size_mb = file_size / 1024 / 1024

                # 获取图片尺寸
                img_info = ""
                try:
                    from PIL import Image
                    with Image.open(filepath) as img:
                        width, height = img.size
                        img_info = f"实际尺寸: {width}x{height}"
                except:
                    img_info = f"设置尺寸: {test['width']}x{test.get('height', '动态')}"

                html_content += f"""
        <div class="screenshot-item">
            <div class="screenshot-title">{test['name']}</div>
            <div class="screenshot-info">
                文件大小: {file_size_mb:.2f} MB<br>
                {img_info}<br>
                文件名: {test['filename']}
            </div>
            <img src="{test['filename']}" alt="{test['name']}" class="screenshot-img" onclick="openModal(this)">
        </div>
"""

        html_content += """
    </div>

    <div class="stats">
        <h2>📊 截图统计</h2>
        <p>本次测试生成了多种分辨率和设置的截图，您可以点击图片查看大图，选择质量最佳的版本。</p>
        <p><strong>建议</strong>：通常2K或4K分辨率的截图质量更好，但文件也更大。根据您的需求选择合适的版本。</p>
    </div>

    <!-- 模态框 -->
    <div id="myModal" class="modal">
        <span class="close" onclick="closeModal()">&times;</span>
        <img class="modal-content" id="img01">
    </div>

    <script>
        function openModal(img) {
            var modal = document.getElementById("myModal");
            var modalImg = document.getElementById("img01");
            modal.style.display = "block";
            modalImg.src = img.src;
        }

        function closeModal() {
            document.getElementById("myModal").style.display = "none";
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            var modal = document.getElementById("myModal");
            if (event.target == modal) {
                modal.style.display = "none";
            }
        }
    </script>
</body>
</html>
"""

        # 保存HTML报告
        report_path = os.path.join(debug_dir, f"screenshot_comparison_{timestamp}.html")
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        print(f"质量对比报告已生成: {report_path}")
        print(f"在浏览器中打开此文件可查看所有截图对比")

    except Exception as e:
        print(f"生成对比报告失败: {e}")

if __name__ == '__main__':
    debug_screenshot()