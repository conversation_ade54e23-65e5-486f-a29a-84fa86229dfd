#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AutoSSH 服务器监控工具
主包初始化文件
"""

__version__ = "1.2.0"
__author__ = "李锦春"
__description__ = "AutoSSH 服务器监控工具"

# 导出主要类和函数
from .main import main
from .core.ssh_agent import SSHAgent
from .core.config_loader import Config
from .web.web_server import WebServer
from .automation.browser import Browser
from .utils.file_check import get_cloud_file_size, generate_file_check_report

__all__ = [
    'main',
    'SSHAgent', 
    'Config',
    'WebServer',
    'Browser',
    'get_cloud_file_size',
    'generate_file_check_report'
]
