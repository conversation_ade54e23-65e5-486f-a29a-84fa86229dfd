# AutoSSH 打包说明

## 快速打包

### 一键打包
```bash
# 双击运行或在命令行执行
build.bat
```

### 打包过程
1. **环境检查** - 验证Python和PyInstaller
2. **依赖安装** - 自动安装requirements.txt中的依赖
3. **清理文件** - 删除旧的打包文件
4. **执行打包** - 使用PyInstaller打包
5. **结果验证** - 检查打包是否成功

## 打包结果

### 文件位置
- **可执行文件**: `dist/AutoSSH.exe`
- **配置文件**: 自动包含在exe中
- **IP表格**: 自动包含在exe中

### 运行特性
- ✅ 显示cmd窗口和日志
- ✅ 配置文件可修改(运行时会提取到exe同目录)
- ✅ IP表格正确读取(从data/ip.xlsx)
- ✅ 独立运行，无需Python环境

## 使用方法

### 直接运行
```bash
# 双击运行
AutoSSH.exe

# 命令行运行
AutoSSH.exe

# 查看版本
AutoSSH.exe --version

# 调试模式
AutoSSH.exe --debug
```

### 配置文件
- 首次运行会自动创建配置文件到exe同目录
- 可直接编辑 `config/config.py` 文件
- 修改后重启程序生效

### IP表格
- 程序会读取 `data/ip.xlsx` 文件
- 支持Excel格式的IP列表
- 可直接替换或编辑该文件

## 故障排除

### 打包失败
1. 检查Python环境是否正确安装
2. 确保网络连接正常(需要下载依赖)
3. 检查磁盘空间是否充足

### 运行问题
1. 确保exe文件完整
2. 检查配置文件格式是否正确
3. 验证IP表格文件是否存在

## 技术细节

### 打包配置
- 使用PyInstaller
- 配置文件: `AutoSSH.spec`
- 打包模式: 单文件exe
- 控制台模式: 启用

### 包含文件
- 配置文件 (config/)
- 数据文件 (data/)
- 图标文件 (ico.ico)
- 文档文件 (docs/)
