# AutoSSH 2.0 服务器监控工具

> 🚀 专业的服务器批量监控和巡检工具，支持SSH批量连接、Web可视化展示和自动截图功能

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Version](https://img.shields.io/badge/Version-1.2.0-orange.svg)](CHANGELOG.md)

## ✨ 核心特性

- 🔗 **批量SSH连接** - 并发连接多台服务器，高效执行监控命令
- 🌐 **Web可视化界面** - 美观的终端风格界面，实时展示结果
- 📸 **自动截图保存** - 一键截图所有服务器状态，便于存档
- 📁 **文件一致性检查** - 智能对比本地与云端文件，5%容差检测
- 🎨 **简洁日志输出** - 优化的日志格式，关键信息一目了然
- ⚙️ **灵活配置管理** - 支持多用户配置，自定义命令和参数

## 📁 项目结构

```
autossh2.0/
├── src/                    # 源代码目录
│   ├── autossh/           # 主包
│   │   ├── __init__.py
│   │   ├── core/          # 核心模块
│   │   │   ├── __init__.py
│   │   │   ├── ssh_agent.py      # SSH连接代理
│   │   │   ├── config_loader.py  # 配置加载器
│   │   │   └── logger.py         # 日志系统
│   │   ├── web/           # Web相关
│   │   │   ├── __init__.py
│   │   │   └── web_server.py     # Web服务器
│   │   ├── automation/    # 自动化相关
│   │   │   ├── __init__.py
│   │   │   └── browser.py        # 浏览器自动化
│   │   ├── utils/         # 工具模块
│   │   │   ├── __init__.py
│   │   │   ├── file_check.py     # 文件检查
│   │   │   └── utils.py          # 通用工具
│   │   └── main.py        # 主程序
│   └── launcher.py        # 启动器
├── config/                # 配置文件目录
│   ├── config.py          # 主配置文件
│   └── config_template.py # 配置模板
├── data/                  # 数据文件目录
│   └── ip.xlsx           # 服务器信息Excel文件
├── output/                # 输出目录
│   ├── logs/             # 日志文件
│   ├── screenshots/      # 截图文件
│   └── reports/          # 报告文件
├── scripts/               # 脚本目录
│   └── build.py          # 构建脚本
├── docs/                  # 文档目录
│   ├── README.md         # 原始文档
│   └── 优化说明.md       # 优化说明
├── requirements.txt       # Python依赖
├── AutoSSH.spec          # PyInstaller配置
└── ico.ico               # 应用图标
```

## 🚀 快速开始

### 环境要求

- Python 3.7+
- Windows/Linux/macOS

### 安装依赖

```bash
pip install -r requirements.txt
```

### 配置设置

1. 复制配置模板：
   ```bash
   copy config\config_template.py config\config.py
   ```

2. 编辑 `config/config.py` 文件，修改以下配置：
   - `USER_NAME`: 您的姓名
   - `COMMANDS`: 要执行的命令
   - `FILE_CHECK_ENABLED`: 是否启用文件监控
   - 其他连接参数

3. 准备服务器数据：
   - 编辑 `data/ip.xlsx` 文件
   - 包含列：ip、密码、门店名称

### 运行程序

```bash
# 开发环境运行
python src/launcher.py

# 或者直接运行主程序
python src/autossh/main.py
```

### 构建可执行文件

```bash
# 使用构建脚本
python scripts/build.py

# 或者手动构建
pyinstaller AutoSSH.spec
```

## 🔧 配置说明

### 主要配置项

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| `USER_NAME` | 用户姓名，用于生成截图目录 | "您的姓名" |
| `EXCEL_PATH` | Excel文件路径 | "ip.xlsx" |
| `SSH_PORT` | SSH端口 | 22 |
| `SSH_USER` | SSH用户名 | "root" |
| `SSH_TIMEOUT` | SSH连接超时(秒) | 15 |
| `MAX_WORKERS` | 最大并发数 | 5 |
| `WEB_PORT` | Web服务端口 | 5000 |
| `COMMANDS` | 执行的命令 | 系统信息查询命令 |

### 文件监控配置

| 配置项 | 说明 |
|--------|------|
| `FILE_CHECK_ENABLED` | 是否启用文件监控 |
| `FILE_TO_CHECK` | 要检查的文件名 |
| `FILE_PATH` | 文件所在路径 |
| `CLOUD_FILE_URL` | 云端文件URL |

## 📊 功能模块

### 核心模块 (core/)

- **ssh_agent.py**: SSH连接管理，支持并发连接和命令执行
- **config_loader.py**: 动态配置加载和管理
- **logger.py**: 统一日志系统，支持文件和控制台输出

### Web模块 (web/)

- **web_server.py**: Flask Web服务器，提供终端风格界面

### 自动化模块 (automation/)

- **browser.py**: 基于DrissionPage的浏览器自动化截图

### 工具模块 (utils/)

- **file_check.py**: 文件一致性检查和报告生成
- **utils.py**: 通用工具函数

## 🛠️ 开发指南

### 添加新功能

1. 在相应的模块目录下创建新文件
2. 在 `__init__.py` 中导出新的类或函数
3. 在主程序中导入和使用

### 修改配置

1. 在 `config/config.py` 中添加新配置项
2. 在 `config_loader.py` 中添加默认值
3. 在相应模块中使用配置

### 调试模式

```bash
python src/launcher.py --debug
```

## 📝 更新日志

### v1.2.0 (当前版本)

- ✅ 重构项目结构，采用模块化设计
- ✅ 分离配置、数据、输出目录
- ✅ 优化代码组织和导入关系
- ✅ 改进错误处理和资源管理
- ✅ 添加构建脚本和文档

### v1.1.0

- ✅ 添加文件监控功能
- ✅ 支持Web可视化界面
- ✅ 实现自动截图功能

### v1.0.0

- ✅ 基础SSH批量连接功能
- ✅ 命令执行和结果收集

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📄 许可证

本项目采用MIT许可证。

## 👨‍💻 作者

李锦春

---

**AutoSSH** - 让服务器监控变得简单高效！
