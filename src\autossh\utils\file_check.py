#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
文件检查模块
处理文件一致性检查功能
"""

import os
import time
import requests
from urllib.parse import urlparse
from ..core.logger import get_logger

# 获取日志实例
log = get_logger()

def get_cloud_file_size(url):
    """获取云端文件大小
    
    Args:
        url (str): 云端文件URL
        
    Returns:
        float: 文件大小(MB)，如果获取失败则返回None
    """
    try:
        log.debug(f"正在获取云端文件大小: {url}")
        response = requests.head(url, timeout=10)
        if response.status_code == 200:
            # 获取Content-Length头部
            content_length = response.headers.get('Content-Length')
            if content_length:
                # 转换为MB并返回
                size_mb = int(content_length) / (1024 * 1024)
                log.debug(f"云端文件大小: {size_mb:.2f}MB")
                return size_mb
        log.warning(f"获取云端文件大小失败，状态码: {response.status_code}")
        return None
    except Exception as e:
        log.error(f"获取云端文件大小出错: {str(e)}")
        return None

def generate_file_check_report(file_check_results, reports_dir, file_to_check, cloud_file_url):
    """生成文件检查报告
    
    Args:
        file_check_results (list): 文件检查结果列表
        reports_dir (str): 报告保存目录
        file_to_check (str): 要检查的文件名
        cloud_file_url (str): 云端文件URL
        
    Returns:
        str: 报告文件路径
    """
    if not file_check_results:
        log.warning("没有文件检查结果，不生成报告")
        return None
        
    # 统计数据
    exist_count = len([r for r in file_check_results if r.file_exists])
    consistent_count = len([r for r in file_check_results if r.is_consistent])
    inconsistent_count = len([r for r in file_check_results if r.file_exists and not r.is_consistent])
    missing_count = len([r for r in file_check_results if not r.file_exists and not r.error])
    error_count = len([r for r in file_check_results if r.error])
    
    # 云端文件信息
    cloud_size = next((r.cloud_size for r in file_check_results if r.cloud_size is not None), None)
    
    # 报告文件路径
    report_path = os.path.join(reports_dir, "文件检查报告.txt")
    
    try:
        # 简化URL显示
        url_display = urlparse(cloud_file_url).path.split("/")[-1]
        
        with open(report_path, "w", encoding="utf-8") as f:
            # 简化报告格式
            f.write("=" * 50 + "\n")
            f.write(f"文件监控报告 - {file_to_check}\n")
            f.write("=" * 50 + "\n")
            f.write(f"检查时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"云端文件: {url_display}\n")
            
            if cloud_size is not None:
                f.write(f"云端大小: {cloud_size:.2f}MB\n\n")
            else:
                f.write("云端大小: 未知\n\n")
            
            f.write("统计数据:\n")
            f.write("-" * 50 + "\n")
            f.write(f"总服务器: {len(file_check_results)}台\n")
            f.write(f"存在文件: {exist_count}台\n")
            f.write(f"大小一致: {consistent_count}台\n")
            f.write(f"大小不一致: {inconsistent_count}台\n")
            f.write(f"缺失文件: {missing_count}台\n")
            f.write(f"检查失败: {error_count}台\n\n")
            
            if consistent_count > 0:
                f.write("大小一致的服务器:\n")
                f.write("-" * 50 + "\n")
                for r in sorted([r for r in file_check_results if r.is_consistent], key=lambda x: x.store):
                    size_str = f"{r.local_size:.2f}MB" if r.local_size is not None else "未知"
                    f.write(f"[一致] {r.store} - {r.ip} ({size_str})\n")
                f.write("\n")
            
            if inconsistent_count > 0:
                f.write("大小不一致的服务器:\n")
                f.write("-" * 50 + "\n")
                for r in sorted([r for r in file_check_results if r.file_exists and not r.is_consistent], key=lambda x: x.store):
                    local_str = f"{r.local_size:.2f}MB" if r.local_size is not None else "未知"
                    cloud_str = f"{r.cloud_size:.2f}MB" if r.cloud_size is not None else "未知"
                    f.write(f"[不同] {r.store} - {r.ip} (本地:{local_str}, 云端:{cloud_str})\n")
                f.write("\n")
            
            if missing_count > 0:
                f.write("文件缺失的服务器:\n")
                f.write("-" * 50 + "\n")
                for r in sorted([r for r in file_check_results if not r.file_exists and not r.error], key=lambda x: x.store):
                    f.write(f"[缺失] {r.store} - {r.ip}\n")
                f.write("\n")
            
            if error_count > 0:
                f.write("检查失败的服务器:\n")
                f.write("-" * 50 + "\n")
                for r in sorted([r for r in file_check_results if r.error], key=lambda x: x.store):
                    f.write(f"[错误] {r.store} - {r.ip} ({r.error})\n")
        
        log.info(f"文件检查报告已保存: {report_path}")
        return report_path
    except Exception as e:
        log.error(f"生成文件检查报告失败: {str(e)}")
        return None

def print_file_check_summary(file_check_results, cloud_file_url):
    """打印文件检查结果摘要

    Args:
        file_check_results (list): 文件检查结果列表
        cloud_file_url (str): 云端文件URL
    """
    if not file_check_results:
        return

    exist_count = len([r for r in file_check_results if r.file_exists])
    consistent_count = len([r for r in file_check_results if r.is_consistent])
    inconsistent_count = len([r for r in file_check_results if r.file_exists and not r.is_consistent])
    missing_count = len([r for r in file_check_results if not r.file_exists and not r.error])
    error_count = len([r for r in file_check_results if r.error])

    # 检查是否为简洁模式
    try:
        from ..core.config_loader import config
        is_simple_mode = getattr(config, 'LOG_SIMPLE_MODE', False)
    except ImportError:
        is_simple_mode = False

    if is_simple_mode:
        # 简洁模式：只显示标题，不显示分隔线
        log.clean_info("\n文件检查结果:")
    else:
        # 标准模式：显示完整分隔线
        log.section(f"文件检查结果摘要")
    
    # 使用更整洁的格式显示结果统计
    log.info(f"✓ 文件存在：{exist_count} 台")
    log.info(f"✓ 大小一致：{consistent_count} 台")
    
    if inconsistent_count > 0:
        log.warning(f"! 大小不一致：{inconsistent_count} 台")
    else:
        log.info(f"✓ 大小不一致：{inconsistent_count} 台")
        
    if missing_count > 0:
        log.warning(f"! 文件缺失：{missing_count} 台")
    else:
        log.info(f"✓ 文件缺失：{missing_count} 台")
        
    if error_count > 0:
        log.error(f"× 检查失败：{error_count} 台")
    else:
        log.info(f"✓ 检查失败：{error_count} 台")
    
    # 云端文件信息
    cloud_size = next((r.cloud_size for r in file_check_results if r.cloud_size is not None), None)
    if cloud_size is not None:
        # 简化URL显示
        url_display = urlparse(cloud_file_url).path.split("/")[-1]
        log.info(f"\n云端文件信息：")
        log.info(f"  • 文件名：{url_display}")
        log.info(f"  • 文件大小：{cloud_size:.2f} MB")
    
    # 一致文件列表
    if consistent_count > 0:
        if is_simple_mode:
            log.clean_info("文件大小一致的服务器:")
        else:
            log.section("文件大小一致的服务器")
        for i, r in enumerate(sorted([r for r in file_check_results if r.is_consistent], key=lambda x: x.store)):
            size_str = f"{r.local_size:.2f} MB" if r.local_size is not None else "未知"
            log.info(f"  {i+1}. {r.store} - {r.ip} ({size_str})")
    
    # 不一致文件列表
    if inconsistent_count > 0:
        if is_simple_mode:
            log.clean_info("【需要注意】大小不一致的文件:")
        else:
            log.section("【需要注意】大小不一致的文件")
        for i, r in enumerate(sorted([r for r in file_check_results if r.file_exists and not r.is_consistent], key=lambda x: x.store)):
            local_str = f"{r.local_size:.2f} MB" if r.local_size is not None else "未知"
            cloud_str = f"{r.cloud_size:.2f} MB" if r.cloud_size is not None else "未知"
            log.warning(f"  {i+1}. {r.store} - {r.ip}")
            log.warning(f"     本地: {local_str} | 云端: {cloud_str}")
    
    # 缺失文件列表
    if missing_count > 0:
        if is_simple_mode:
            log.clean_info("【需要安装】缺失文件的服务器:")
        else:
            log.section("【需要安装】缺失文件的服务器")
        for i, r in enumerate(sorted([r for r in file_check_results if not r.file_exists and not r.error], key=lambda x: x.store)):
            log.warning(f"  {i+1}. {r.store} - {r.ip}")

    # 检查失败列表
    if error_count > 0:
        if is_simple_mode:
            log.clean_info("【检查失败】的服务器:")
        else:
            log.section("【检查失败】的服务器")
        for i, r in enumerate(sorted([r for r in file_check_results if r.error], key=lambda x: x.store)):
            log.error(f"  {i+1}. {r.store} - {r.ip}")
            log.error(f"     错误: {r.error}")
            
    log.info("")  # 添加空行
