
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>截图质量对比报告 - 20250820_195752</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .screenshot-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .screenshot-item {
            background: white;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .screenshot-item:hover {
            transform: translateY(-5px);
        }
        .screenshot-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .screenshot-info {
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
        }
        .screenshot-img {
            width: 100%;
            border: 1px solid #ddd;
            border-radius: 5px;
            cursor: pointer;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.9);
        }
        .modal-content {
            margin: auto;
            display: block;
            width: 90%;
            max-width: 1200px;
            max-height: 90%;
            margin-top: 2%;
        }
        .close {
            position: absolute;
            top: 15px;
            right: 35px;
            color: #f1f1f1;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
        }
        .stats {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📸 截图质量对比报告</h1>
        <p>生成时间: 20250820_195752</p>
        <p>测试URL: http://127.0.0.1:5000/server/10.108.100.4</p>
    </div>

    <div class="screenshot-grid">

        <div class="screenshot-item">
            <div class="screenshot-title">标准截图 1920x1080</div>
            <div class="screenshot-info">
                文件大小: 0.02 MB<br>
                实际尺寸: 1878x987<br>
                文件名: debug_standard_20250820_195752.png
            </div>
            <img src="debug_standard_20250820_195752.png" alt="标准截图 1920x1080" class="screenshot-img" onclick="openModal(this)">
        </div>

        <div class="screenshot-item">
            <div class="screenshot-title">高清截图 2560x1440</div>
            <div class="screenshot-info">
                文件大小: 0.03 MB<br>
                实际尺寸: 2518x1347<br>
                文件名: debug_2k_20250820_195752.png
            </div>
            <img src="debug_2k_20250820_195752.png" alt="高清截图 2560x1440" class="screenshot-img" onclick="openModal(this)">
        </div>

        <div class="screenshot-item">
            <div class="screenshot-title">4K截图 3840x2160</div>
            <div class="screenshot-info">
                文件大小: 0.04 MB<br>
                实际尺寸: 3798x2067<br>
                文件名: debug_4k_20250820_195752.png
            </div>
            <img src="debug_4k_20250820_195752.png" alt="4K截图 3840x2160" class="screenshot-img" onclick="openModal(this)">
        </div>

        <div class="screenshot-item">
            <div class="screenshot-title">全页面截图</div>
            <div class="screenshot-info">
                文件大小: 0.02 MB<br>
                实际尺寸: 1878x896<br>
                文件名: debug_fullpage_20250820_195752.png
            </div>
            <img src="debug_fullpage_20250820_195752.png" alt="全页面截图" class="screenshot-img" onclick="openModal(this)">
        </div>

        <div class="screenshot-item">
            <div class="screenshot-title">全页面高清截图</div>
            <div class="screenshot-info">
                文件大小: 0.02 MB<br>
                实际尺寸: 2518x896<br>
                文件名: debug_fullpage_hd_20250820_195752.png
            </div>
            <img src="debug_fullpage_hd_20250820_195752.png" alt="全页面高清截图" class="screenshot-img" onclick="openModal(this)">
        </div>

        <div class="screenshot-item">
            <div class="screenshot-title">移动端视图截图</div>
            <div class="screenshot-info">
                文件大小: 0.01 MB<br>
                实际尺寸: 474x719<br>
                文件名: debug_mobile_20250820_195752.png
            </div>
            <img src="debug_mobile_20250820_195752.png" alt="移动端视图截图" class="screenshot-img" onclick="openModal(this)">
        </div>

        <div class="screenshot-item">
            <div class="screenshot-title">平板视图截图</div>
            <div class="screenshot-info">
                文件大小: 0.02 MB<br>
                实际尺寸: 726x931<br>
                文件名: debug_tablet_20250820_195752.png
            </div>
            <img src="debug_tablet_20250820_195752.png" alt="平板视图截图" class="screenshot-img" onclick="openModal(this)">
        </div>

        <div class="screenshot-item">
            <div class="screenshot-title">超宽屏截图</div>
            <div class="screenshot-info">
                文件大小: 0.03 MB<br>
                实际尺寸: 3398x1347<br>
                文件名: debug_ultrawide_20250820_195752.png
            </div>
            <img src="debug_ultrawide_20250820_195752.png" alt="超宽屏截图" class="screenshot-img" onclick="openModal(this)">
        </div>

    </div>

    <div class="stats">
        <h2>📊 截图统计</h2>
        <p>本次测试生成了多种分辨率和设置的截图，您可以点击图片查看大图，选择质量最佳的版本。</p>
        <p><strong>建议</strong>：通常2K或4K分辨率的截图质量更好，但文件也更大。根据您的需求选择合适的版本。</p>
    </div>

    <!-- 模态框 -->
    <div id="myModal" class="modal">
        <span class="close" onclick="closeModal()">&times;</span>
        <img class="modal-content" id="img01">
    </div>

    <script>
        function openModal(img) {
            var modal = document.getElementById("myModal");
            var modalImg = document.getElementById("img01");
            modal.style.display = "block";
            modalImg.src = img.src;
        }

        function closeModal() {
            document.getElementById("myModal").style.display = "none";
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            var modal = document.getElementById("myModal");
            if (event.target == modal) {
                modal.style.display = "none";
            }
        }
    </script>
</body>
</html>
