#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
工具模块
提供通用工具函数
"""

import os
import sys
import time
import platform
import atexit
import threading
import psutil
import shutil
import tempfile
from ..core.logger import get_logger

# 获取日志实例
log = get_logger()

# 临时文件跟踪
_temp_files = set()
_temp_dirs = set()
_temp_lock = threading.Lock()

def register_temp_file(filepath):
    """注册临时文件，确保程序退出时删除
    
    Args:
        filepath (str): 文件路径
    
    Returns:
        str: 文件路径
    """
    with _temp_lock:
        _temp_files.add(os.path.abspath(filepath))
    return filepath

def register_temp_dir(dirpath):
    """注册临时目录，确保程序退出时删除
    
    Args:
        dirpath (str): 目录路径
    
    Returns:
        str: 目录路径
    """
    with _temp_lock:
        _temp_dirs.add(os.path.abspath(dirpath))
    return dirpath

def create_temp_file(prefix="autossh_", suffix=".tmp"):
    """创建临时文件
    
    Args:
        prefix (str, optional): 文件名前缀. 默认为 "autossh_".
        suffix (str, optional): 文件扩展名. 默认为 ".tmp".
    
    Returns:
        str: 临时文件路径
    """
    fd, filepath = tempfile.mkstemp(suffix=suffix, prefix=prefix)
    os.close(fd)  # 关闭文件描述符
    return register_temp_file(filepath)

def create_temp_dir(prefix="autossh_"):
    """创建临时目录
    
    Args:
        prefix (str, optional): 目录名前缀. 默认为 "autossh_".
    
    Returns:
        str: 临时目录路径
    """
    dirpath = tempfile.mkdtemp(prefix=prefix)
    return register_temp_dir(dirpath)

def cleanup_temp_files():
    """清理所有注册的临时文件和目录"""
    # 先清理文件
    with _temp_lock:
        for filepath in list(_temp_files):
            try:
                if os.path.exists(filepath):
                    os.remove(filepath)
                    log.debug(f"已删除临时文件: {filepath}")
            except Exception as e:
                log.error(f"删除临时文件失败: {filepath} - {str(e)}")
        _temp_files.clear()
        
        # 再清理目录
        for dirpath in list(_temp_dirs):
            try:
                if os.path.exists(dirpath):
                    shutil.rmtree(dirpath, ignore_errors=True)
                    log.debug(f"已删除临时目录: {dirpath}")
            except Exception as e:
                log.error(f"删除临时目录失败: {dirpath} - {str(e)}")
        _temp_dirs.clear()

# 注册退出时清理
atexit.register(cleanup_temp_files)

def get_system_info():
    """获取系统信息
    
    Returns:
        dict: 系统信息字典
    """
    info = {
        'platform': platform.platform(),
        'python_version': platform.python_version(),
        'hostname': platform.node(),
        'cpu_count': psutil.cpu_count(),
        'memory_total': round(psutil.virtual_memory().total / (1024 * 1024 * 1024), 2)
    }
    return info

def format_bytes(size):
    """格式化字节大小
    
    Args:
        size (int): 字节大小
    
    Returns:
        str: 格式化后的大小
    """
    if size < 1024:
        return f"{size}B"
    elif size < 1024 * 1024:
        return f"{size / 1024:.2f}KB"
    elif size < 1024 * 1024 * 1024:
        return f"{size / (1024 * 1024):.2f}MB"
    else:
        return f"{size / (1024 * 1024 * 1024):.2f}GB"

def format_time(seconds):
    """格式化时间
    
    Args:
        seconds (float): 秒数
    
    Returns:
        str: 格式化后的时间
    """
    if seconds < 60:
        return f"{seconds:.2f}秒"
    elif seconds < 3600:
        m, s = divmod(seconds, 60)
        return f"{int(m)}分{int(s)}秒"
    else:
        h, remainder = divmod(seconds, 3600)
        m, s = divmod(remainder, 60)
        return f"{int(h)}时{int(m)}分{int(s)}秒"

def retry(times=3, exceptions=Exception, delay=0):
    """重试装饰器
    
    Args:
        times (int, optional): 重试次数. 默认为 3.
        exceptions (Exception, optional): 异常类型. 默认为 Exception.
        delay (float, optional): 延迟时间. 默认为 0.
    
    Returns:
        function: 装饰后的函数
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            attempt = 0
            while attempt < times:
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    attempt += 1
                    if attempt == times:
                        raise
                    if delay > 0:
                        time.sleep(delay)
        return wrapper
    return decorator 
