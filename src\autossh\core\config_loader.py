#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
配置加载模块
负责加载和管理配置项
"""

import os
import sys
import time
import re
import glob
import importlib.util
import importlib
from .logger import get_logger

# 获取日志实例
log = get_logger()

class Config:
    """配置管理类"""
    
    def __init__(self):
        """初始化配置管理器"""
        self.app_path = self.get_app_path()
        
        # 默认配置
        self.EXCEL_PATH = ""
        self.SCREENSHOTS_DIR = ""
        self.COMMANDS = ""
        self.USER_NAME = ""
        self.SSH_PORT = 22
        self.SSH_USER = 'root'
        self.SSH_TIMEOUT = 15
        self.CMD_TIMEOUT = 30
        self.MAX_WORKERS = 5
        self.WEB_HOST = '0.0.0.0'
        self.WEB_PORT = 5000
        self.STORE_NAME_WIDTH = 10
        self.BROWSER_WINDOW_SIZE = (1024, 768)
        self.SCREENSHOT_DELAY = 0.5
        self.TERMINAL_FONT_SIZE = 15
        self.TERMINAL_LINE_HEIGHT = 1.5
        self.COLOR_SUCCESS = "32"
        self.COLOR_ERROR = "31"
        self.COLOR_WARNING = "33"
        self.COLOR_INFO = "36"
        self.FILE_CHECK_ENABLED = True
        self.FILE_TO_CHECK = ""
        self.FILE_PATH = ""
        self.CLOUD_FILE_URL = ""
        
    def get_app_path(self):
        """获取应用程序路径
        
        Returns:
            str: 应用程序路径
        """
        if getattr(sys, 'frozen', False):
            # 如果是打包后的exe
            return os.path.dirname(sys.executable)
        else:
            # 如果是python脚本，返回项目根目录
            # 从 src/autossh/core/config_loader.py 回到根目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            # 向上三级目录到达项目根目录
            return os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
    
    def load(self):
        """加载配置文件

        Returns:
            bool: 是否加载成功
        """
        # 配置文件在 config 目录下
        config_path = os.path.join(self.app_path, 'config', 'config.py')
        try:
            if os.path.exists(config_path):
                log.info(f"加载配置文件: {config_path}")

                # 删除已存在的config模块（如果有）
                if 'config' in sys.modules:
                    del sys.modules['config']

                # 动态加载配置文件
                spec = importlib.util.spec_from_file_location('config', config_path)
                config = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(config)

                # 将配置变量添加到对象属性
                for name in dir(config):
                    if not name.startswith('_'):
                        setattr(self, name, getattr(config, name))

                # 自动检测Excel文件和用户名（如果配置中没有设置）
                if not hasattr(self, 'USER_NAME') or not self.USER_NAME or not hasattr(self, 'EXCEL_PATH') or not self.EXCEL_PATH:
                    excel_info = self.auto_detect_excel_file()
                    if excel_info:
                        if not hasattr(self, 'USER_NAME') or not self.USER_NAME:
                            self.USER_NAME = excel_info['username']
                        if not hasattr(self, 'EXCEL_PATH') or not self.EXCEL_PATH:
                            self.EXCEL_PATH = excel_info['filename']

                # 设置截图目录
                self.SCREENSHOTS_DIR = self.get_screenshots_dir()

                # 打印关键配置信息
                self.print_config()
                return True
            else:
                log.error(f"配置文件不存在: {config_path}")
                return False
        except Exception as e:
            log.error(f"配置文件加载失败: {str(e)}")
            return False
    
    def auto_detect_excel_file(self):
        """自动检测Excel文件和用户名

        Returns:
            dict: 包含filename和username的字典，如果失败返回None
        """
        data_dir = os.path.join(self.app_path, 'data')
        if not os.path.exists(data_dir):
            log.error(f"数据目录不存在: {data_dir}")
            return None

        # 扫描Excel文件
        excel_files = []
        for ext in ['*.xlsx', '*.xls']:
            excel_files.extend(glob.glob(os.path.join(data_dir, ext)))

        if not excel_files:
            log.error("data目录中没有找到Excel文件")
            return None

        # 提取文件信息
        file_info_list = []
        for file_path in excel_files:
            filename = os.path.basename(file_path)
            username = self.extract_username_from_filename(filename)
            file_info_list.append({
                'filepath': file_path,
                'filename': filename,
                'username': username or '未知用户'
            })

        # 如果只有一个文件，自动使用
        if len(file_info_list) == 1:
            file_info = file_info_list[0]
            log.info(f"✓ 自动检测到Excel文件: {file_info['filename']}")
            log.info(f"✓ 提取用户名: {file_info['username']}")
            return file_info

        # 如果有多个文件，让用户选择
        return self.select_excel_file(file_info_list)

    def extract_username_from_filename(self, filename):
        """从文件名中提取中文用户名

        Args:
            filename (str): 文件名

        Returns:
            str: 提取的用户名，如果失败返回None
        """
        # 移除文件扩展名
        name_without_ext = os.path.splitext(filename)[0]

        # 匹配中文姓名的正则表达式（2-4个中文字符）
        chinese_name_pattern = r'[\u4e00-\u9fff]{2,4}'

        # 尝试多种提取模式
        patterns = [
            # 直接是中文姓名
            r'^([\u4e00-\u9fff]{2,4})$',
            # 以中文姓名结尾（如：服务器列表-李锦春）
            r'-([\u4e00-\u9fff]{2,4})$',
            # 以中文姓名结尾（如：服务器列表_李锦春）
            r'_([\u4e00-\u9fff]{2,4})$',
            # 中文姓名在中间（如：巡检表_李锦春_2025）
            r'_([\u4e00-\u9fff]{2,4})_',
            # 任何位置的中文姓名
            r'([\u4e00-\u9fff]{2,4})'
        ]

        for pattern in patterns:
            match = re.search(pattern, name_without_ext)
            if match:
                return match.group(1)

        return None

    def select_excel_file(self, file_info_list):
        """让用户选择Excel文件

        Args:
            file_info_list (list): 文件信息列表

        Returns:
            dict: 选择的文件信息，如果失败返回None
        """
        log.info("发现多个Excel文件，请选择：")
        for i, file_info in enumerate(file_info_list, 1):
            log.info(f"[{i}] {file_info['filename']} (用户名: {file_info['username']})")

        while True:
            try:
                choice = input(f"请输入序号 [1-{len(file_info_list)}]: ").strip()
                if not choice:
                    continue

                choice_num = int(choice)
                if 1 <= choice_num <= len(file_info_list):
                    selected_file = file_info_list[choice_num - 1]
                    log.info(f"✓ 已选择: {selected_file['filename']}")
                    log.info(f"✓ 用户名: {selected_file['username']}")
                    return selected_file
                else:
                    log.warning(f"请输入1到{len(file_info_list)}之间的数字")
            except ValueError:
                log.warning("请输入有效的数字")
            except KeyboardInterrupt:
                log.info("\n用户取消选择")
                return None

    def get_screenshots_dir(self):
        """生成截图保存目录名称
        
        Returns:
            str: 截图保存目录名称
        """
        if hasattr(self, 'SCREENSHOTS_DIR') and self.SCREENSHOTS_DIR:
            return self.SCREENSHOTS_DIR
        
        # 直接使用系统当前日期，去掉月份前导零
        import datetime
        now = datetime.datetime.now()
        current_date = f"{now.year}-{now.month}"  # 去掉月份前导零
        return f"{current_date} 备用服务器巡-{self.USER_NAME}"
    
    def ensure_screenshots_dir(self):
        """确保截图目录存在
        
        Returns:
            str: 截图目录路径
        """
        # 截图保存在 output/screenshots 目录下
        screenshots_base_dir = os.path.join(self.app_path, 'output', 'screenshots')
        screenshots_dir = os.path.join(screenshots_base_dir, self.SCREENSHOTS_DIR)
        
        if not os.path.exists(screenshots_dir):
            try:
                os.makedirs(screenshots_dir)
                log.info(f"创建截图目录: {screenshots_dir}")
            except Exception as e:
                log.error(f"创建截图目录失败: {str(e)}")
                raise
        return screenshots_dir
    
    def get_excel_path(self):
        """获取Excel文件路径
        
        Returns:
            str: Excel文件完整路径
        """
        return os.path.join(self.app_path, 'data', self.EXCEL_PATH)
    
    def get_logs_dir(self):
        """获取日志目录路径
        
        Returns:
            str: 日志目录路径
        """
        logs_dir = os.path.join(self.app_path, 'output', 'logs')
        if not os.path.exists(logs_dir):
            os.makedirs(logs_dir)
        return logs_dir
    
    def get_reports_dir(self):
        """获取报告目录路径
        
        Returns:
            str: 报告目录路径
        """
        reports_dir = os.path.join(self.app_path, 'output', 'reports')
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)
        return reports_dir
    
    def print_config(self):
        """打印配置信息"""
        log.info(f"截图保存目录: {self.SCREENSHOTS_DIR}")
        log.info(f"Excel文件路径: {self.EXCEL_PATH}")
        log.info(f"执行的命令: {self.COMMANDS}")
        
        # 打印更多配置信息
        log.debug(f"SSH端口: {self.SSH_PORT}")
        log.debug(f"SSH用户名: {self.SSH_USER}")
        log.debug(f"SSH连接超时: {self.SSH_TIMEOUT}秒")
        log.debug(f"命令执行超时: {self.CMD_TIMEOUT}秒")
        log.debug(f"最大并发数: {self.MAX_WORKERS}")
        log.debug(f"WEB监听地址: {self.WEB_HOST}:{self.WEB_PORT}")
        
        if self.FILE_CHECK_ENABLED:
            log.debug(f"文件检查: 启用")
            log.debug(f"检查文件: {self.FILE_TO_CHECK}")
            log.debug(f"文件路径: {self.FILE_PATH}")
            log.debug(f"云端URL: {self.CLOUD_FILE_URL}")

# 创建全局配置实例
config = Config()
