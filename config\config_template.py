#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AutoSSH 配置模板文件
复制此文件为 config.py 并根据实际需要修改配置项
"""

# ========== 基本配置 ==========
USER_NAME = "您的姓名"                # 用户姓名，用于生成截图目录名称

# ========== 文件和路径配置 ==========
EXCEL_PATH = "ip.xlsx"               # Excel文件路径（相对于data目录）

# ========== 连接配置 ==========
SSH_PORT = 22                        # SSH端口
SSH_USER = 'root'                    # SSH用户名
SSH_TIMEOUT = 15                     # SSH连接超时(秒)
CMD_TIMEOUT = 30                     # 命令执行超时(秒)
MAX_WORKERS = 5                      # 最大并发数

# ========== WEB服务配置 ==========
WEB_HOST = '0.0.0.0'                # WEB监听地址
WEB_PORT = 5000                     # WEB监听端口

# ========== 显示配置 ==========
STORE_NAME_WIDTH = 10                # 门店名称显示宽度
BROWSER_WINDOW_SIZE = (1024, 768)    # 浏览器窗口尺寸
SCREENSHOT_DELAY = 0.5               # 截图前等待时间(秒)
TERMINAL_FONT_SIZE = 15              # 终端字体大小(像素)
TERMINAL_LINE_HEIGHT = 1.5           # 终端行高

# ========== 颜色配置 ==========
COLOR_SUCCESS = "32"                 # 成功颜色(绿色)
COLOR_ERROR = "31"                   # 错误颜色(红色)
COLOR_WARNING = "33"                 # 警告颜色(黄色)
COLOR_INFO = "36"                    # 信息颜色(青色)

# ========== 命令配置 ==========
# 要在每台服务器上执行的命令，多个命令用分号分隔
# 示例命令：
# - cat /etc/redhat-release: 显示系统版本
# - date: 显示当前时间
# - uptime: 显示系统运行时间
# - ls -lh /opt: 列出/opt目录内容
# - df -h: 显示磁盘使用情况
# - free -h: 显示内存使用情况
COMMANDS = "cat /etc/redhat-release;date +%Y-%m-%d,%H:%M,%n;uptime;ls -lh /opt;df -h;free -h" 

# ========== 文件监控配置 ==========
FILE_CHECK_ENABLED = True            # 是否启用文件监控功能
FILE_TO_CHECK = "pos_china_lastest.tar.gz"  # 需要检查的文件名
FILE_PATH = "/opt"                   # 文件所在路径
CLOUD_FILE_URL = "https://example.com/file.tar.gz"  # 云端文件URL（请替换为实际URL）

# ========== 高级配置 ==========
# 以下配置项一般不需要修改
DISABLE_COLORS = True                # 是否禁用颜色输出
SCREENSHOTS_DIR = None               # 截图保存目录（自动生成，无需修改）
