# AutoSSH 服务器监控工具

AutoSSH是一个强大的服务器监控工具，可以批量连接多台服务器，执行命令，检查文件一致性，并生成可视化监控报告。

## 主要功能

- **批量SSH连接**：通过Excel文件读取服务器信息，并发连接多台服务器
- **命令执行**：在远程服务器上执行预设命令
- **文件一致性检查**：检查远程服务器上的文件是否存在及其大小是否与云端一致
- **Web可视化**：通过内置Web服务器展示执行结果
- **自动截图**：使用浏览器自动化工具对执行结果进行截图保存
- **报告生成**：生成文件检查报告

## 系统架构

- **模块化设计**：各功能模块职责明确，耦合度低
- **并发处理**：使用线程池提高并发效率
- **安全连接管理**：确保所有SSH连接被正确关闭
- **错误恢复机制**：完善的错误处理和日志记录
- **美观的界面**：终端风格的Web界面，支持ANSI颜色转换

## 安装说明

1. 确保安装了Python 3.7+
2. 安装所需依赖库：
   ```
   pip install -r requirements.txt
   ```
3. 配置`config.py`文件

## 使用说明

1. 确保`ip.xlsx`文件与程序在同一目录
2. 双击`AutoSSH.exe`启动程序（或运行`python autossh_launcher.py`）
3. 程序会自动连接服务器并执行监控命令
4. 截图会保存在当前目录下的年月命名文件夹中
5. 文件检查报告会保存为"文件检查报告.txt"

## 配置文件

`config.py`文件用于配置程序行为，主要参数包括：
- 连接配置（端口、用户名、超时等）
- Web服务器配置
- 显示设置
- 命令配置
- 文件监控配置

## 依赖库列表

- **paramiko**: SSH连接和命令执行
- **pandas**: 数据处理和Excel文件读取
- **flask**: Web服务器
- **DrissionPage**: 浏览器自动化和截图
- **requests**: HTTP请求
- **ansi2html**: ANSI转HTML
- **psutil**: 系统信息获取
- **openpyxl**: Excel文件处理

## 版本信息

当前版本: 1.2.0
构建时间: 2025-03-23 02:09:20
构建环境: Python 3.12.1 on Windows 10

## 联系方式

作者: 李锦春
