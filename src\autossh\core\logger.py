#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
重构后的日志模块
提供简洁清晰的日志记录功能
"""

import os
import logging
import threading
from logging.handlers import RotatingFileHandler

# 日志级别映射
LOG_LEVELS = {
    'debug': logging.DEBUG,
    'info': logging.INFO,
    'warning': logging.WARNING,
    'error': logging.ERROR,
    'critical': logging.CRITICAL
}

# 线程锁用于同步输出
_print_lock = threading.Lock()

# 全局日志实例
_logger_instance = None


class LogFilter(logging.Filter):
    """统一的日志过滤器"""
    
    def __init__(self, simple_mode=False):
        super().__init__()
        self.simple_mode = simple_mode
        
        # 定义需要过滤的SSH相关关键词
        self.ssh_keywords = {
            'auth', 'connection', 'negotiate', 'transport', 
            'connected', 'successful', 'authentication',
            'password', 'client', 'version', 'openssh',
            'banner', 'kex', 'cipher', 'mac', 'compression'
        }
        
        # 简洁模式下额外过滤的关键词
        self.simple_keywords = {
            '正在连接', 'connecting', '已关闭', 'closed'
        }
    
    def filter(self, record):
        """过滤日志记录"""
        if not hasattr(record, 'msg') or not isinstance(record.msg, str):
            return True
            
        # 过滤paramiko库的所有日志
        if 'paramiko' in record.name.lower():
            return False
        
        msg_lower = record.msg.lower()
        
        # 过滤SSH相关信息
        if any(keyword in msg_lower for keyword in self.ssh_keywords):
            return False
        
        # 简洁模式下过滤更多信息
        if self.simple_mode and any(keyword in msg_lower for keyword in self.simple_keywords):
            return False
            
        return True


class BaseLogger:
    """基础日志记录器"""
    
    def __init__(self, name='autossh', level='info', log_file=None, 
                 max_bytes=10*1024*1024, backup_count=5):
        """初始化基础日志记录器"""
        self.name = name
        self.level = LOG_LEVELS.get(level.lower(), logging.INFO)
        self.log_file = log_file
        self.max_bytes = max_bytes
        self.backup_count = backup_count
        
        # 创建logger
        self.logger = logging.getLogger(name)
        self.logger.setLevel(self.level)

        # 清除已有的处理器
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)

        # 设置paramiko库的日志级别为WARNING，避免显示连接详情
        logging.getLogger('paramiko').setLevel(logging.WARNING)
        logging.getLogger('paramiko.transport').setLevel(logging.WARNING)
        logging.getLogger('paramiko.transport.sftp').setLevel(logging.WARNING)
    
    def _add_console_handler(self):
        """添加控制台处理器"""
        console_handler = logging.StreamHandler()
        console_handler.setLevel(self.level)
        formatter = logging.Formatter('%(message)s')
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
    
    def _add_file_handler(self):
        """添加文件处理器"""
        if not self.log_file:
            return
            
        # 确保日志目录存在
        log_dir = os.path.dirname(self.log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # 创建文件处理器
        file_handler = RotatingFileHandler(
            self.log_file,
            maxBytes=self.max_bytes,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(self.level)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
    
    # 基础日志方法
    def debug(self, msg): self.logger.debug(msg)
    def info(self, msg): self.logger.info(msg)
    def warning(self, msg): self.logger.warning(msg)
    def error(self, msg): self.logger.error(msg)
    def critical(self, msg): self.logger.critical(msg)


class StandardLogger(BaseLogger):
    """标准模式日志记录器"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.logger.addFilter(LogFilter(simple_mode=False))
        self._add_console_handler()
        self._add_file_handler()

    def section(self, title, char='─', length=60):
        """显示章节标题"""
        if not title:
            separator = char * length
            self.logger.info(f"\n{separator}\n")
            return

        # 计算分隔线长度
        title_length = len(title) + 2
        remaining_length = length - title_length
        side_length = max(1, remaining_length // 2)

        left_sep = char * side_length
        right_sep = char * (length - side_length - title_length)

        self.logger.info("")
        self.logger.info(f"{left_sep} {title} {right_sep}")
        self.logger.info("")

    def progress(self, store, ip, status, message="", duration=0):
        """显示进度信息"""
        try:
            # 获取配置宽度
            store_width = 10
            try:
                from .config_loader import config
                store_width = getattr(config, 'STORE_NAME_WIDTH', 10)
            except ImportError:
                pass

            # 格式化输出
            duration_str = f" ({duration:.1f}s)" if duration > 0 else ""
            log_msg = f"[{status}] {store:<{store_width}} - {ip:<15} {message}{duration_str}"
            self.logger.info(log_msg)

        except Exception:
            # 简单格式作为后备
            self.logger.info(f"[{status}] {store} - {ip} {message}")

    def clean_info(self, message):
        """清洁信息输出（标准模式下等同于info）"""
        self.logger.info(message)


class SimpleLogger(BaseLogger):
    """简洁模式日志记录器"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.logger.addFilter(LogFilter(simple_mode=True))
        self._add_simple_console_handler()  # 使用专门的简洁控制台处理器
        self._add_file_handler()

    def _add_simple_console_handler(self):
        """添加简洁模式的控制台处理器"""
        # 移除所有现有的处理器
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)

        # 创建新的控制台处理器，完全不显示时间戳
        console_handler = logging.StreamHandler()
        console_handler.setLevel(self.level)
        formatter = logging.Formatter('%(message)s')
        console_handler.setFormatter(formatter)

        # 设置传播为False，避免被父logger处理
        self.logger.propagate = False
        self.logger.addHandler(console_handler)

    def section(self, title, char='─', length=60):
        """简洁模式的章节标题"""
        if title and not title.startswith("按回车键"):
            self.logger.info(f"\n{title}")

    def progress(self, store, ip, status, message="", duration=0):
        """简洁模式的进度信息"""
        if status == "√":
            duration_str = f" - {duration:.1f}s" if duration > 0 else ""
            self.logger.info(f"√ {store} ({ip}){duration_str}")
        elif status == "×":
            self.logger.info(f"× {store} ({ip}) - {message}")

    def clean_info(self, message):
        """清洁信息输出"""
        self.logger.info(message)

    def progress_bar(self, current, total, success=0, failed=0, width=20):
        """显示进度条"""
        if total == 0:
            return

        # 计算进度
        progress = current / total
        filled = int(width * progress)
        bar = '=' * filled + '-' * (width - filled)

        # 状态信息
        status_info = ""
        if success > 0 or failed > 0:
            status_info = f" | 成功:{success} 失败:{failed}"

        # 根据进度显示不同的文本
        if current == 0:
            progress_text = f"任务开始... [{bar}] {current}/{total}{status_info}"
        elif current == total:
            progress_text = f"任务完成! [{bar}] {current}/{total}{status_info}"
        else:
            progress_text = f"任务进行中... [{bar}] {current}/{total}{status_info}"

        # 正常显示进度条（在下面滚动）
        self.clean_info(progress_text)

    # 为了兼容性，添加simple_progress_bar别名
    def simple_progress_bar(self, current, total, success=0, failed=0, width=20):
        """进度条别名（向后兼容）"""
        self.progress_bar(current, total, success, failed, width)


def create_logger(simple_mode=False, name='autossh', level='info',
                 log_file=None, **kwargs):
    """工厂函数：创建日志记录器

    Args:
        simple_mode (bool): 是否使用简洁模式
        name (str): 日志名称
        level (str): 日志级别
        log_file (str): 日志文件路径
        **kwargs: 其他参数

    Returns:
        BaseLogger: 日志记录器实例
    """
    if simple_mode:
        return SimpleLogger(name=name, level=level, log_file=log_file, **kwargs)
    else:
        return StandardLogger(name=name, level=level, log_file=log_file, **kwargs)


def get_logger(name='autossh', level='info', log_file=None, console=True,
               force_new=False, simple_mode=False):
    """获取日志记录器（保持向后兼容）

    Args:
        name (str): 日志名称
        level (str): 日志级别
        log_file (str): 日志文件路径
        console (bool): 是否输出到控制台（保持兼容性）
        force_new (bool): 是否强制创建新实例
        simple_mode (bool): 是否使用简洁模式

    Returns:
        BaseLogger: 日志记录器实例
    """
    global _logger_instance

    if _logger_instance is None or force_new:
        _logger_instance = create_logger(
            simple_mode=simple_mode,
            name=name,
            level=level,
            log_file=log_file
        )

    return _logger_instance


# 为了向后兼容，保留Logger类的别名
Logger = BaseLogger