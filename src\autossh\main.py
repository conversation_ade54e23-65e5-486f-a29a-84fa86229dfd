#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AutoSSH 服务器监控工具
主程序入口点
"""

import warnings
# 忽略 TripleDES 的废弃警告
warnings.filterwarnings("ignore", category=DeprecationWarning, module="paramiko")
warnings.filterwarnings("ignore", category=DeprecationWarning, module="cryptography")
# 更精确地忽略特定警告
warnings.filterwarnings("ignore", message="TripleDES has been moved to")

import os
import sys
import time
import pandas as pd
from concurrent.futures import ThreadPoolExecutor, as_completed

# 导入自定义模块
from .core.logger import get_logger
from .core.config_loader import config
from .core.ssh_agent import SSHAgent
from .web.web_server import WebServer
from .automation.browser import Browser
from .utils.file_check import get_cloud_file_size, generate_file_check_report, print_file_check_summary

# 获取日志实例 - 稍后在配置加载后重新初始化
log = None

def show_welcome():
    """显示欢迎信息"""
    if hasattr(config, 'LOG_SIMPLE_MODE') and config.LOG_SIMPLE_MODE:
        # 简洁模式的欢迎信息
        version = "v1.2.0"
        user = getattr(config, 'USER_NAME', '未知用户')
        log.clean_info(f"AutoSSH {version} | 用户: {user}")
    else:
        # 标准模式的欢迎信息
        log.section("AutoSSH 服务器监控工具", "=")
        log.info("初始化中...\n")

def check_excel_file():
    """检查Excel文件是否存在

    Returns:
        str: Excel文件路径，如果不存在则返回None
    """
    # 如果配置中没有设置Excel路径，说明自动检测失败
    if not hasattr(config, 'EXCEL_PATH') or not config.EXCEL_PATH:
        log.error("未能自动检测到Excel文件，请检查data目录")
        return None

    excel_path = config.get_excel_path()
    if not os.path.exists(excel_path):
        log.error(f"Excel文件不存在: {excel_path}")
        return None

    if hasattr(config, 'LOG_SIMPLE_MODE') and config.LOG_SIMPLE_MODE:
        # 简洁模式下不显示这些详细信息
        pass
    else:
        log.info(f"✓ 使用Excel文件: {config.EXCEL_PATH}")
        log.info(f"✓ 当前用户: {config.USER_NAME}")
    return excel_path

def load_server_data(excel_path):
    """加载服务器数据
    
    Args:
        excel_path (str): Excel文件路径
        
    Returns:
        pandas.DataFrame: 服务器数据DataFrame
    """
    try:
        df = pd.read_excel(excel_path, engine='openpyxl')
        df = df[['ip', '密码', '门店名称']].dropna()
        
        # 显示配置信息
        if hasattr(config, 'LOG_SIMPLE_MODE') and config.LOG_SIMPLE_MODE:
            # 简洁模式：一行显示关键信息
            file_status = "启用" if config.FILE_CHECK_ENABLED else "禁用"
            log.clean_info(f"服务器: {len(df)}台 | 并发: {config.MAX_WORKERS} | 文件监控: {file_status}")
        else:
            # 标准模式
            log.section("服务器监控配置")
            log.info(f"服务器: {len(df)} 台")
            log.info(f"并发数: {config.MAX_WORKERS}")

            if config.FILE_CHECK_ENABLED:
                log.info("文件监控: 启用")
                log.info(f"监控文件: {config.FILE_TO_CHECK}")
            else:
                log.info("文件监控: 禁用")

            log.info("")
            log.info("正在连接服务器...")
        
        return df
    except Exception as e:
        log.error(f"Excel文件加载失败: {str(e)}")
        return None

def process_servers(df):
    """处理服务器连接
    
    Args:
        df (pandas.DataFrame): 服务器数据DataFrame
        
    Returns:
        tuple: (web_results, file_check_results, success_count, error_ips)
    """
    web_results = []
    file_check_results = []
    success_ips = []
    error_ips = []
    
    # 获取云端文件大小
    cloud_size = None
    if config.FILE_CHECK_ENABLED:
        cloud_size = get_cloud_file_size(config.CLOUD_FILE_URL)

    # 任务进度跟踪
    total_tasks = len(df)
    completed_tasks = 0
    is_simple_mode = hasattr(config, 'LOG_SIMPLE_MODE') and config.LOG_SIMPLE_MODE

    # 显示初始任务进度（已移除进度条）
    # if is_simple_mode:
    #     log.progress_bar(0, total_tasks, 0, 0)

    with ThreadPoolExecutor(max_workers=config.MAX_WORKERS) as executor:
        futures = []

        for _, row in df.iterrows():
            store = row['门店名称']
            ip = row['ip']
            password = row['密码']

            # 标准模式下显示连接中状态
            if not (hasattr(config, 'LOG_SIMPLE_MODE') and config.LOG_SIMPLE_MODE):
                log.progress(store, ip, "*", "连接中...")

            # 创建SSH代理
            agent = SSHAgent(
                ip,
                password,
                store,
                port=config.SSH_PORT,
                username=config.SSH_USER,
                timeout=config.SSH_TIMEOUT,
                cmd_timeout=config.CMD_TIMEOUT
            )

            # 提交任务
            future = executor.submit(
                agent.execute,
                config.COMMANDS,
                config.FILE_CHECK_ENABLED,
                config.FILE_TO_CHECK,
                config.FILE_PATH,
                config.CLOUD_FILE_URL,
                cloud_size,
                file_check_results
            )

            futures.append((future, agent, row))
        
        # 处理结果
        success_count = 0

        for future, agent, row in futures:
            try:
                result = future.result()
                completed_tasks += 1

                if result:
                    success_count += 1
                    success_ips.append((row['门店名称'], row['ip']))
                    web_results.append(agent.get_web_result())
                    # 显示成功结果
                    log.progress(row['门店名称'], row['ip'], "√", "连接成功", agent.duration if hasattr(agent, 'duration') else 0)
                else:
                    error_ips.append((row['门店名称'], row['ip']))
                    log.progress(row['门店名称'], row['ip'], "×", "连接失败")

                # 更新任务进度（已移除进度条）
                # if is_simple_mode:
                #     log.progress_bar(completed_tasks, total_tasks, success_count, len(error_ips))

            except Exception as e:
                completed_tasks += 1
                error_ips.append((row['门店名称'], row['ip']))
                log.progress(row['门店名称'], row['ip'], "×", f"连接失败: {str(e)}")

                # 更新任务进度（已移除进度条）
                # if is_simple_mode:
                #     log.progress_bar(completed_tasks, total_tasks, success_count, len(error_ips))
    
    # 连接完成信息
    if hasattr(config, 'LOG_SIMPLE_MODE') and config.LOG_SIMPLE_MODE:
        # 简洁模式：只显示失败的服务器
        if error_ips:
            log.clean_info("\n连接失败的服务器:")
            for store, ip in sorted(error_ips, key=lambda x: x[0]):
                log.clean_info(f"× {store} ({ip})")
    else:
        # 标准模式：显示详细信息
        log.section(f"连接完成: {success_count}成功 / {len(error_ips)}失败")

        # 显示成功连接的服务器
        if success_ips:
            log.info("成功连接的服务器:")
            for i, (store, ip) in enumerate(sorted(success_ips, key=lambda x: x[0])):
                log.info(f"  {i+1}. {store} - {ip}")
            log.info("")  # 添加空行增加可读性

        # 显示失败的服务器
        if error_ips:
            log.info("连接失败的服务器:")
            for i, (store, ip) in enumerate(sorted(error_ips, key=lambda x: x[0])):
                log.info(f"  {i+1}. {store} - {ip}")
            log.info("")  # 添加空行增加可读性
    
    return web_results, file_check_results, success_count, error_ips

def show_completion_message(success_count, screenshots_dir, report_path=None):
    """显示完成信息

    Args:
        success_count (int): 成功连接的服务器数量
        screenshots_dir (str): 截图保存目录
        report_path (str, optional): 报告文件路径. 默认为 None.
    """
    if hasattr(config, 'LOG_SIMPLE_MODE') and config.LOG_SIMPLE_MODE:
        # 简洁模式：一行显示结果
        report_info = f" | 报告: {os.path.basename(report_path)}" if report_path else ""
        log.clean_info(f"\n结果: {success_count}成功 | 截图: {os.path.basename(screenshots_dir)}{report_info}")
    else:
        # 标准模式
        log.section("任务完成摘要")
        log.info(f"✓ 成功处理服务器: {success_count}台")
        log.info(f"✓ 截图保存位置: {os.path.basename(screenshots_dir)}")

        if report_path:
            log.info(f"✓ 文件检查报告: {os.path.basename(report_path)}")

        log.section("按回车键退出程序")

def main():
    """主程序入口"""
    global log
    try:
        # 先加载配置
        if not config.load():
            print("配置加载失败，程序退出")
            return 1

        # 根据配置初始化日志
        simple_mode = getattr(config, 'LOG_SIMPLE_MODE', False)
        log = get_logger(simple_mode=simple_mode, force_new=True)

        # 显示欢迎信息
        show_welcome()
        
        # 确保截图目录存在
        screenshots_dir = config.ensure_screenshots_dir()
        
        # 检查Excel文件
        excel_path = check_excel_file()
        if not excel_path:
            return 1
        
        # 加载服务器数据
        df = load_server_data(excel_path)
        if df is None:
            return 1
        
        # 启动Web服务
        web_server = WebServer(
            config.WEB_HOST, 
            config.WEB_PORT,
            config.TERMINAL_FONT_SIZE,
            config.TERMINAL_LINE_HEIGHT
        )
        web_server.start()
        
        # 处理服务器连接
        web_results, file_check_results, success_count, error_ips = process_servers(df)
        
        # 为Web服务器添加结果
        for result in web_results:
            web_server.add_result(result)
        
        # 如果没有成功连接的服务器，退出
        if not web_results:
            log.error("没有成功连接的服务器，无法继续")
            return 1
        
        # 生成文件检查报告（但不显示摘要，留到最后显示）
        report_path = None
        if config.FILE_CHECK_ENABLED and file_check_results:
            report_path = generate_file_check_report(
                file_check_results,
                config.get_reports_dir(),
                config.FILE_TO_CHECK,
                config.CLOUD_FILE_URL
            )
        
        # 自动截图
        browser = Browser(
            screenshots_dir, 
            config.BROWSER_WINDOW_SIZE,
            config.SCREENSHOT_DELAY
        )
        browser.auto_screenshot_all(web_results, config.WEB_PORT)

        # 显示文件检查结果摘要（放在最后）
        if config.FILE_CHECK_ENABLED and file_check_results:
            print_file_check_summary(file_check_results, config.CLOUD_FILE_URL)

        # 显示完成信息
        show_completion_message(success_count, screenshots_dir, report_path)
        
        # 等待用户按键退出
        input()
        return 0
    
    except KeyboardInterrupt:
        log.info("\n用户中断执行")
        return 130
    except Exception as e:
        log.section("程序运行出错")
        log.error(f"× 错误信息: {str(e)}")
        log.error(f"× 错误类型: {type(e).__name__}")
        log.error(f"× 错误位置: {sys.exc_info()[2].tb_frame.f_code.co_filename}:{sys.exc_info()[2].tb_lineno}")
        
        log.section("按回车键退出程序")
        input()
        return 1

if __name__ == '__main__':
    # 设置系统编码
    if sys.stdout.encoding != 'utf-8':
        try:
            sys.stdout.reconfigure(encoding='utf-8')
        except:
            pass
    
    sys.exit(main())
