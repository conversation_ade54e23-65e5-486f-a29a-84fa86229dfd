#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AutoSSH 配置文件
请根据实际需要修改以下配置项
"""

# ========== 基本配置 ==========
# 用户姓名和Excel文件路径现在自动检测，无需手动配置
# 系统会自动扫描data目录下的Excel文件，并从文件名中提取用户姓名
# 如果需要手动指定，可以取消下面的注释：
# USER_NAME = "李锦春"                 # 用户姓名，用于生成截图目录名称
# EXCEL_PATH = "ip2.xlsx"             # Excel文件路径（相对于data目录）

# ========== 文件和路径配置 ==========
# Excel文件现在自动检测，支持以下命名格式：
# - 李锦春.xlsx (直接用户名)
# - 服务器列表-李锦春.xlsx (带前缀)
# - 巡检表_李锦春_2025.xlsx (复杂格式)

# ========== 连接配置 ==========
SSH_PORT = 22                        # SSH端口
SSH_USER = 'root'                    # SSH用户名
SSH_TIMEOUT = 15                     # SSH连接超时(秒)
CMD_TIMEOUT = 30                     # 命令执行超时(秒)
MAX_WORKERS = 5                      # 最大并发数

# ========== WEB服务配置 ==========
WEB_HOST = '0.0.0.0'                # WEB监听地址
WEB_PORT = 5000                     # WEB监听端口

# ========== 显示配置 ==========
STORE_NAME_WIDTH = 10                # 门店名称显示宽度
BROWSER_WINDOW_SIZE = (1024, 768)    # 浏览器窗口尺寸
SCREENSHOT_DELAY = 0.5               # 截图前等待时间(秒)
TERMINAL_FONT_SIZE = 15              # 终端字体大小(像素)
TERMINAL_LINE_HEIGHT = 1.5           # 终端行高

# ========== 日志配置 ==========
LOG_SIMPLE_MODE = True               # 启用简洁模式日志输出
SHOW_PROGRESS_BAR = True             # 显示进度条

# ========== 颜色配置 ==========
COLOR_SUCCESS = "32"                 # 成功颜色(绿色)
COLOR_ERROR = "31"                   # 错误颜色(红色)
COLOR_WARNING = "33"                 # 警告颜色(黄色)
COLOR_INFO = "36"                    # 信息颜色(青色)

# ========== 命令配置 ==========
# 要在每台服务器上执行的命令，多个命令用分号分隔
COMMANDS = "cat /etc/redhat-release;date +%Y-%m-%d,%H:%M,%n;uptime;ls -lh /opt;df -h;free -h" 

# ========== 文件监控配置 ==========
FILE_CHECK_ENABLED = True            # 是否启用文件监控功能
FILE_TO_CHECK = "pos_china_lastest.tar.gz"  # 需要检查的文件名
FILE_PATH = "/opt"                   # 文件所在路径
CLOUD_FILE_URL = "https://jcss-patrol-file.oss-cn-beijing.aliyuncs.com/shoptools/scripts/pos_china_lastest.tar.gz"  # 云端文件URL

# ========== 高级配置 ==========
# 以下配置项一般不需要修改
DISABLE_COLORS = True                # 是否禁用颜色输出
SCREENSHOTS_DIR = None               # 截图保存目录（自动生成，无需修改）
