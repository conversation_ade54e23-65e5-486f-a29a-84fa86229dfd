#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
浏览器自动化模块
处理自动截图功能
"""

import os
import time
import sys
from DrissionPage import ChromiumPage, ChromiumOptions
from ..core.logger import get_logger

# 获取日志实例
log = get_logger()

class Browser:
    """浏览器自动化类"""
    
    def __init__(self, screenshots_dir, window_size=(1024, 768), delay=0.5):
        """初始化浏览器
        
        Args:
            screenshots_dir (str): 截图保存目录
            window_size (tuple, optional): 浏览器窗口大小. 默认为 (1024, 768).
            delay (float, optional): 截图前等待时间. 默认为 0.5.
        """
        self.screenshots_dir = screenshots_dir
        self.window_size = window_size
        self.delay = delay
        self.page = None
    
    def init(self):
        """初始化浏览器实例"""
        try:
            co = ChromiumOptions().headless()
            self.page = ChromiumPage(co)
            self.page.set.window.size(*self.window_size)
            log.info("浏览器已初始化")
            return True
        except Exception as e:
            log.error(f"浏览器初始化失败: {str(e)}")
            return False
    
    def screenshot_server(self, ip, store, web_port=5000):
        """对服务器结果页面截图
        
        Args:
            ip (str): 服务器IP
            store (str): 门店名称
            web_port (int, optional): Web服务端口. 默认为 5000.
            
        Returns:
            str: 截图文件路径，失败则返回None
        """
        try:
            # 访问服务器详情页
            url = f'http://localhost:{web_port}/server/{ip}'
            log.debug(f"正在访问页面: {url}")
            self.page.get(url)
            time.sleep(self.delay)
            
            # 获取终端元素并截图
            terminal = self.page('css:.terminal')
            if not terminal:
                log.error(f"未找到终端元素: {ip}")
                return None
                
            # 使用当前年月作为文件名前缀，去掉月份前导零
            import datetime
            now = datetime.datetime.now()
            current_date = f"{now.year}-{now.month}"  # 去掉月份前导零
            # 格式化文件名
            filename = f"{current_date} {store}备用服务器运行情况.png"
            
            # 截图
            filepath = terminal.get_screenshot(
                path=self.screenshots_dir,
                name=filename,
                scroll_to_center=True
            )
            
            log.info(f"截图已保存: {filename}")
            return filepath
        except Exception as e:
            log.error(f"截图失败: {ip} ({store}) - {str(e)}")
            return None
    
    def auto_screenshot_all(self, web_results, web_port=5000):
        """自动为所有服务器结果截图
        
        Args:
            web_results (list): Web结果列表
            web_port (int, optional): Web服务端口. 默认为 5000.
            
        Returns:
            int: 成功截图的数量
        """
        if not self.page:
            if not self.init():
                return 0
        
        try:
            total = len(web_results)
            success_count = 0
            
            # 检查是否为简洁模式
            try:
                from ..core.config_loader import config
                is_simple_mode = getattr(config, 'LOG_SIMPLE_MODE', False)
            except ImportError:
                is_simple_mode = False

            # 简化信息显示
            if is_simple_mode:
                log.clean_info(f"\n开始截图: {total}台服务器")
            else:
                log.section("服务器截图任务")
                log.info(f"服务器: {total}台")
                log.info(f"保存目录: {os.path.basename(self.screenshots_dir)}")
            
            for idx, result in enumerate(web_results, 1):
                progress = f"{idx}/{total} ({idx*100//total}%)"
                
                ip = result['ip']
                store = result['store']
                
                log.progress(store, ip, "*", f"截图中... {progress}")
                
                if self.screenshot_server(ip, store, web_port):
                    success_count += 1
                    log.progress(store, ip, "√", f"截图完成 {progress}")
                else:
                    log.progress(store, ip, "×", f"截图失败 {progress}")
            
            # 任务完成信息
            if is_simple_mode:
                log.clean_info(f"截图完成: {success_count}/{total}台")
            else:
                log.section("服务器监控任务完成")
                log.info(f"总服务器: {total}台")
                log.info(f"成功截图: {success_count}台")
                log.info(f"保存目录: {os.path.basename(self.screenshots_dir)}")
            
            return success_count
        except Exception as e:
            log.error(f"自动截图过程中出错: {str(e)}")
            return 0
        finally:
            self.close()
    
    def close(self):
        """关闭浏览器"""
        if self.page:
            try:
                self.page.quit()
                log.debug("浏览器已关闭")
            except Exception as e:
                log.error(f"关闭浏览器出错: {str(e)}")
            self.page = None
