# AutoSSH 2.0 项目结构

## 📁 目录结构

```
autossh2.0/
├── 📁 config/                    # 配置文件目录
│   ├── config.py                 # 主配置文件
│   └── config_template.py        # 配置模板文件
├── 📁 data/                      # 数据文件目录
│   ├── 李锦 - 副本.xlsx          # 用户数据文件
│   ├── 李锦春.xlsx               # 用户数据文件
│   └── 李鸿泽.xlsx               # 用户数据文件
├── 📁 docs/                      # 文档目录
│   ├── README.md                 # 项目说明文档
│   ├── 优化版使用指南.md         # 使用指南
│   ├── 优化说明.md               # 优化说明
│   ├── 日志模块重构总结.md       # 重构总结
│   └── 项目重构总结.md           # 项目总结
├── 📁 issues/                    # 问题跟踪目录
│   └── 打包命令任务.md           # 打包相关问题
├── 📁 output/                    # 输出文件目录
│   ├── 📁 logs/                  # 日志文件
│   ├── 📁 reports/               # 报告文件
│   └── 📁 screenshots/           # 截图文件
├── 📁 scripts/                   # 脚本工具目录
│   ├── cleanup_old_files.py      # 清理脚本
│   ├── project_status.py         # 项目状态检查
│   └── test_structure.py         # 结构测试
├── 📁 src/                       # 源代码目录
│   ├── 📁 autossh/               # 主程序包
│   │   ├── 📁 automation/        # 自动化模块
│   │   ├── 📁 core/              # 核心模块
│   │   ├── 📁 utils/             # 工具模块
│   │   ├── 📁 web/               # Web服务模块
│   │   └── main.py               # 主程序入口
│   ├── launcher.py               # 启动器
│   └── __init__.py               # 包初始化文件
├── .gitignore                    # Git忽略文件
├── AutoSSH.spec                  # PyInstaller配置
├── build.bat                     # 构建脚本
├── ico.ico                       # 程序图标
├── README.md                     # 项目主文档
├── requirements.txt              # 依赖包列表
├── 启动AutoSSH.bat               # 启动脚本
└── 打包说明.md                   # 打包说明文档
```

## 📋 文件说明

### 🔧 核心文件
- **src/launcher.py**: 程序启动入口
- **src/autossh/main.py**: 主程序逻辑
- **config/config.py**: 配置文件
- **requirements.txt**: Python依赖包

### 📊 数据文件
- **data/*.xlsx**: Excel服务器配置文件
- **output/**: 程序运行输出目录

### 📖 文档文件
- **README.md**: 项目主要说明
- **docs/**: 详细文档目录
- **打包说明.md**: 打包部署说明

### 🛠️ 工具文件
- **build.bat**: 一键构建脚本
- **scripts/**: 辅助工具脚本
- **AutoSSH.spec**: PyInstaller打包配置

## 🚀 快速开始

1. **安装依赖**: `pip install -r requirements.txt`
2. **配置文件**: 编辑 `config/config.py`
3. **运行程序**: 双击 `启动AutoSSH.bat` 或运行 `python src/launcher.py`
4. **打包程序**: 运行 `build.bat`

## 📝 维护说明

- **日志文件**: 自动保存在 `output/logs/`
- **截图文件**: 自动保存在 `output/screenshots/`
- **报告文件**: 自动保存在 `output/reports/`
- **配置备份**: 建议定期备份 `config/` 和 `data/` 目录